<!-- Admin Resources Index -->

<div class="admin-header">
    <div class="flex items-center justify-between">
        <h1 class="admin-title">
            <i class="fas fa-book mr-2 text-purple-600"></i>Resource Management
        </h1>
        <a href="/admin/resources/create" class="btn btn-primary">
            <i class="fas fa-plus mr-2"></i>Add New Resource
        </a>
    </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-book text-purple-600"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900"><%= resources.length %></h3>
                <p class="text-gray-600">Total Resources</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-file-pdf text-blue-600"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900"><%= resources.filter(r => r.type === 'PDF').length %></h3>
                <p class="text-gray-600">PDF Files</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-link text-green-600"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900"><%= resources.filter(r => r.type === 'Link').length %></h3>
                <p class="text-gray-600">External Links</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-calendar text-yellow-600"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900">
                    <%= resources.filter(r => new Date(r.createdAt) > new Date(Date.now() - 30*24*60*60*1000)).length %>
                </h3>
                <p class="text-gray-600">This Month</p>
            </div>
        </div>
    </div>
</div>



<!-- Resources Table -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <% if (resources && resources.length > 0) { %>
        <table class="table admin-table w-full">
            <thead>
                <tr>
                    <th class="w-8">
                        <input type="checkbox" id="select-all" class="form-checkbox">
                    </th>
                    <th>Resource</th>
                    <th>Type</th>
                    <th>Category</th>
                    <th>Created</th>
                    <th class="w-32">Actions</th>
                </tr>
            </thead>
            <tbody id="resources-tbody">
                <% resources.forEach(resource => { %>
                    <tr class="resource-row" data-resource-id="<%= resource._id %>">
                        <td>
                            <input type="checkbox" name="selected_items[]" value="<%= resource._id %>" class="form-checkbox">
                        </td>
                        <td data-title="<%= resource.title %>">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                    <% if (resource.type === 'PDF') { %>
                                        <i class="fas fa-file-pdf text-red-600"></i>
                                    <% } else if (resource.type === 'Link') { %>
                                        <i class="fas fa-link text-blue-600"></i>
                                    <% } else if (resource.type === 'Video') { %>
                                        <i class="fas fa-video text-green-600"></i>
                                    <% } else { %>
                                        <i class="fas fa-file text-purple-600"></i>
                                    <% } %>
                                </div>
                                <div>
                                    <h3 class="font-medium text-gray-900"><%= resource.title %></h3>
                                    <p class="text-sm text-gray-500">
                                        <%= resource.description.length > 60 ? resource.description.substring(0, 60) + '...' : resource.description %>
                                    </p>
                                </div>
                            </div>
                        </td>
                        <td data-type="<%= resource.type %>">
                            <span class="status-badge <%= resource.type === 'PDF' ? 'active' : resource.type === 'Link' ? 'draft' : 'inactive' %>">
                                <i class="fas fa-<%= resource.type === 'PDF' ? 'file-pdf' : resource.type === 'Link' ? 'link' : 'file' %> mr-1"></i>
                                <%= resource.type %>
                            </span>
                        </td>
                        <td data-category="<%= resource.category %>">
                            <span class="text-sm text-gray-900"><%= resource.category %></span>
                        </td>
                        <td data-created="<%= resource.createdAt %>">
                            <div class="text-sm">
                                <div class="text-gray-900"><%= new Date(resource.createdAt).toLocaleDateString() %></div>
                                <div class="text-gray-500"><%= new Date(resource.createdAt).toLocaleTimeString() %></div>
                            </div>
                        </td>
                        <td>
                            <div class="flex items-center space-x-2">
                                <% if (resource.type === 'Link') { %>
                                    <a href="<%= resource.url %>" target="_blank" class="action-btn view" title="Open Link">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                <% } else if (resource.file) { %>
                                    <a href="<%= resource.file %>" target="_blank" class="action-btn view" title="View File">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                <% } %>
                                <a href="/admin/resources/<%= resource._id %>/edit" class="action-btn edit" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="action-btn delete delete-resource-btn" title="Delete" data-resource-id="<%= resource._id %>">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                <% }) %>
            </tbody>
        </table>

        <!-- Bulk Actions -->
        <div id="bulk-actions" class="hidden p-4 bg-gray-50 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">
                    <span id="selected-count">0</span> resources selected
                </span>
                <div class="flex space-x-2">
                    <button class="btn btn-secondary" id="change-category-btn">
                        <i class="fas fa-tag mr-1"></i>Change Category
                    </button>
                    <button class="btn btn-danger" id="delete-selected-btn">
                        <i class="fas fa-trash mr-1"></i>Delete Selected
                    </button>
                </div>
            </div>
        </div>
    <% } else { %>
        <div class="p-12 text-center">
            <div class="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-book text-gray-400 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Resources Found</h3>
            <p class="text-gray-600 mb-6">Get started by adding your first resource.</p>
            <a href="/admin/resources/create" class="btn btn-primary">
                <i class="fas fa-plus mr-2"></i>Add First Resource
            </a>
        </div>
    <% } %>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search-resources');
    const typeFilter = document.getElementById('filter-type');
    const categoryFilter = document.getElementById('filter-category');
    const tbody = document.getElementById('resources-tbody');
    const rows = Array.from(document.querySelectorAll('.resource-row'));
    const selectAllCheckbox = document.getElementById('select-all');
    const bulkActions = document.getElementById('bulk-actions');
    const selectedCount = document.getElementById('selected-count');

    // Search and filter functionality
    function filterResources() {
        const searchTerm = searchInput.value.toLowerCase();
        const typeValue = typeFilter.value;
        const categoryValue = categoryFilter.value;

        rows.forEach(row => {
            const title = row.querySelector('[data-title]').getAttribute('data-title').toLowerCase();
            const type = row.querySelector('[data-type]').getAttribute('data-type');
            const category = row.querySelector('[data-category]').getAttribute('data-category');

            const matchesSearch = title.includes(searchTerm);
            const matchesType = !typeValue || type === typeValue;
            const matchesCategory = !categoryValue || category === categoryValue;

            row.style.display = matchesSearch && matchesType && matchesCategory ? '' : 'none';
        });
    }

    // Bulk selection functionality
    function updateBulkActions() {
        const checkboxes = document.querySelectorAll('input[name="selected_items[]"]');
        const checkedBoxes = document.querySelectorAll('input[name="selected_items[]"]:checked');

        selectedCount.textContent = checkedBoxes.length;
        bulkActions.style.display = checkedBoxes.length > 0 ? 'block' : 'none';

        selectAllCheckbox.checked = checkboxes.length > 0 && checkedBoxes.length === checkboxes.length;
        selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < checkboxes.length;
    }

    // Event listeners
    searchInput.addEventListener('input', filterResources);
    typeFilter.addEventListener('change', filterResources);
    categoryFilter.addEventListener('change', filterResources);

    selectAllCheckbox.addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('input[name="selected_items[]"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });

    document.addEventListener('change', function(e) {
        if (e.target.name === 'selected_items[]') {
            updateBulkActions();
        }
    });

    // Setup delete buttons for individual resources
    const deleteButtons = document.querySelectorAll('.delete-resource-btn');
    deleteButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const resourceId = this.getAttribute('data-resource-id');
            if (resourceId && confirm('Are you sure you want to delete this resource?')) {
                deleteResource(resourceId);
            }
        });
    });

    // Setup bulk action buttons
    const changeCategoryBtn = document.getElementById('change-category-btn');
    const deleteSelectedBtn = document.getElementById('delete-selected-btn');

    if (changeCategoryBtn) {
        changeCategoryBtn.addEventListener('click', changeCategory);
    }

    if (deleteSelectedBtn) {
        deleteSelectedBtn.addEventListener('click', deleteSelected);
    }

    // Initialize
    updateBulkActions();
});

function deleteResource(resourceId) {
    fetch(`/admin/resources/${resourceId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            alert('Resource deleted successfully!');
            location.reload();
        } else {
            alert(data.error || 'Error deleting resource');
        }
    })
    .catch(error => {
        console.error('Delete error:', error);
        alert('Error deleting resource: ' + error.message);
    });
}

function changeCategory() {
    const selectedIds = Array.from(document.querySelectorAll('input[name="selected_items[]"]:checked'))
        .map(cb => cb.value);

    if (selectedIds.length === 0) return;

    const newCategory = prompt('Enter new category:');
    if (newCategory) {
        // Implementation would make AJAX calls to change category
        console.log('Change category to', newCategory, 'for:', selectedIds);
    }
}

function deleteSelected() {
    const selectedIds = Array.from(document.querySelectorAll('input[name="selected_items[]"]:checked'))
        .map(cb => cb.value);

    if (selectedIds.length === 0) return;

    if (confirm(`Are you sure you want to delete ${selectedIds.length} resources?`)) {
        // Implementation would make AJAX calls to delete resources
        console.log('Delete resources:', selectedIds);
    }
}
</script>
