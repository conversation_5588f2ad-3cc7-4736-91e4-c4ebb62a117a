const apiClient = require('../utils/apiClient');
const { validationResult } = require('express-validator');
const multer = require('multer');
const path = require('path');
const Event = require('../models/Event');

// Helper function to get auth token from session
const getAuthToken = (req) => {
  return req.session.authToken || req.session.user?.token;
};

// Get all active events
exports.getAllEvents = async (req, res) => {
  try {
    // Get all events via API
    const response = await apiClient.getEvents();

    if (!response.success) {
      throw new Error(response.message);
    }

    const events = response.data.events || response.data || [];
    const currentDate = new Date();

    // Separate upcoming and past events
    const upcomingEvents = events.filter(event => new Date(event.date) >= currentDate)
      .sort((a, b) => new Date(a.date) - new Date(b.date));

    const pastEvents = events.filter(event => new Date(event.date) < currentDate)
      .sort((a, b) => new Date(b.date) - new Date(a.date));

    res.render('pages/events/index', {
      title: 'Events',
      currentPage: 'events',
      upcomingEvents,
      pastEvents
    });
  } catch (error) {
    console.error('Error fetching events:', error);
    res.status(500).render('pages/error', {
      title: 'Error',
      error: 'Error fetching events'
    });
  }
};

// Get single event by ID
exports.getEventById = async (req, res) => {
  try {
    const response = await apiClient.getEvent(req.params.id);

    if (!response.success) {
      if (response.status === 404) {
        return res.status(404).render('pages/404', {
          title: '404 - Event Not Found'
        });
      }
      throw new Error(response.message);
    }

    const event = response.data;

    res.render('pages/events/show', {
      title: event.title,
      currentPage: 'events',
      event
    });
  } catch (error) {
    console.error('Error fetching event:', error);
    res.status(500).render('pages/error', {
      title: 'Error',
      error: 'Error fetching event'
    });
  }
};

// Configure multer for image uploads - simple disk storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'public/uploads/events');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'event-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files (JPEG, PNG, GIF) are allowed!'));
    }
  }
}).single('image');

// Multer configuration for multiple gallery images
const uploadGallery = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit per file
    files: 10 // Maximum 10 files
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files (JPEG, PNG, GIF) are allowed!'));
    }
  }
}).array('galleryImages', 10); // Accept up to 10 files with field name 'galleryImages'

// Multer configuration for create form (cover + gallery images)
const uploadCreateForm = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit per file
    files: 11 // Maximum 11 files (1 cover + 10 gallery)
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files (JPEG, PNG, GIF) are allowed!'));
    }
  }
}).fields([
  { name: 'image', maxCount: 1 },        // Cover image
  { name: 'galleryImages', maxCount: 10 } // Gallery images
]);

// Get all events for admin dashboard
exports.getAdminEvents = async (req, res) => {
  try {
    // Use direct database access for consistency with other admin methods
    const events = await Event.find().sort({ createdAt: -1 });
    console.log('Fetched events for admin:', events.length);

    res.render('admin/events/index', {
      events,
      title: 'Manage Events',
      user: req.session.user
    });
  } catch (error) {
    console.error('Error fetching admin events:', error);
    req.flash('error', 'Error fetching events');
    res.redirect('/admin/dashboard');
  }
};

// Show create event form
exports.getCreateEventForm = (req, res) => {
  res.render('admin/events/create', {
    title: 'Create New Event',
    user: req.session.user
  });
};

// Create new event
exports.createEvent = async (req, res) => {
  try {
    uploadCreateForm(req, res, async (err) => {
      if (err) {
        console.error('Create form upload error:', err);
        req.flash('error', err.message);
        return res.redirect('/admin/events/create');
      }

      console.log('✅ Create form upload completed');
      console.log('Cover image:', req.files?.image ? req.files.image[0].filename : 'None');
      console.log('Gallery images:', req.files?.galleryImages ? req.files.galleryImages.length : 0);

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        req.flash('error', errors.array()[0].msg);
        return res.redirect('/admin/events/create');
      }

      // Prepare gallery images array
      const galleryImages = req.files?.galleryImages
        ? req.files.galleryImages.map(file => `/uploads/events/${file.filename}`)
        : [];

      const event = new Event({
        title: req.body.title,
        description: req.body.description,
        location: req.body.location,
        date: new Date(req.body.startDate),
        endDate: req.body.endDate ? new Date(req.body.endDate) : null,
        featured: req.body.featured === 'true',
        image: req.files?.image ? `/uploads/events/${req.files.image[0].filename}` : null,
        gallery: galleryImages,
        organizer: 'Science Club',
        registrationLink: req.body.registrationLink || null
      });

      await event.save();

      const successMessage = galleryImages.length > 0
        ? `Event created successfully with ${galleryImages.length} gallery image(s)`
        : 'Event created successfully';

      req.flash('success', successMessage);
      res.redirect('/admin/events');
    });
  } catch (error) {
    console.error('Error creating event:', error);
    req.flash('error', 'Error creating event');
    res.redirect('/admin/events/create');
  }
};

// Get single event for admin view
exports.getAdminEventById = async (req, res) => {
  try {
    const event = await Event.findById(req.params.id);
    if (!event) {
      req.flash('error', 'Event not found');
      return res.redirect('/admin/events');
    }

    res.render('admin/events/show', {
      event,
      title: `Event: ${event.title}`,
      user: req.session.user
    });
  } catch (error) {
    req.flash('error', 'Error fetching event');
    res.redirect('/admin/events');
  }
};

// Show edit event form
exports.getEditEventForm = async (req, res) => {
  try {
    const event = await Event.findById(req.params.id);
    if (!event) {
      req.flash('error', 'Event not found');
      return res.redirect('/admin/events');
    }

    res.render('admin/events/edit', {
      event,
      title: 'Edit Event',
      user: req.session.user
    });
  } catch (error) {
    req.flash('error', 'Error fetching event');
    res.redirect('/admin/events');
  }
};

// Update event
exports.updateEvent = async (req, res) => {
  try {
    upload(req, res, async (err) => {
      if (err) {
        console.error('Upload error:', err);
        req.flash('error', err.message);
        return res.redirect(`/admin/events/${req.params.id}/edit`);
      }

      const event = await Event.findById(req.params.id);
      if (!event) {
        req.flash('error', 'Event not found');
        return res.redirect('/admin/events');
      }

      // Update basic fields
      event.title = req.body.title;
      event.description = req.body.description;
      event.location = req.body.location;
      event.date = new Date(req.body.startDate);
      event.endDate = req.body.endDate ? new Date(req.body.endDate) : null;
      event.featured = req.body.featured === 'true';
      event.organizer = 'Science Club';
      event.registrationLink = req.body.registrationLink || null;

      // Update image if new one is uploaded
      if (req.file) {
        event.image = `/uploads/events/${req.file.filename}`;
      }

      await event.save();
      req.flash('success', 'Event updated successfully');
      res.redirect('/admin/events');
    });
  } catch (error) {
    console.error('Error updating event:', error);
    req.flash('error', 'Error updating event');
    res.redirect(`/admin/events/${req.params.id}/edit`);
  }
};

// Delete event
exports.deleteEvent = async (req, res) => {
  try {
    const event = await Event.findById(req.params.id);
    if (!event) {
      return res.status(404).json({ success: false, error: 'Event not found' });
    }

    await Event.findByIdAndDelete(req.params.id);
    res.json({ success: true, message: 'Event deleted successfully' });
  } catch (error) {
    console.error('Error deleting event:', error);
    res.status(500).json({ success: false, error: 'Error deleting event' });
  }
};

// Toggle event featured status
exports.toggleEventFeatured = async (req, res) => {
  try {
    const event = await Event.findById(req.params.id);
    if (!event) {
      return res.status(404).json({ success: false, error: 'Event not found' });
    }

    event.featured = !event.featured;
    await event.save();

    res.json({
      success: true,
      message: `Event ${event.featured ? 'featured' : 'unfeatured'} successfully`,
      featured: event.featured
    });
  } catch (error) {
    res.status(500).json({ success: false, error: 'Error toggling event featured status' });
  }
};

// Bulk action on events
exports.bulkActionEvents = async (req, res) => {
  try {
    const { action, eventIds } = req.body;

    switch (action) {
      case 'delete':
        await Event.deleteMany({ _id: { $in: eventIds } });
        req.flash('success', 'Selected events deleted successfully');
        break;

      case 'feature':
        await Event.updateMany(
          { _id: { $in: eventIds } },
          { $set: { featured: true } }
        );
        req.flash('success', 'Selected events featured successfully');
        break;

      case 'unfeature':
        await Event.updateMany(
          { _id: { $in: eventIds } },
          { $set: { featured: false } }
        );
        req.flash('success', 'Selected events unfeatured successfully');
        break;

      case 'cancel':
        await Event.updateMany(
          { _id: { $in: eventIds } },
          { $set: { status: 'cancelled' } }
        );
        req.flash('success', 'Selected events cancelled successfully');
        break;

      default:
        req.flash('error', 'Invalid bulk action');
    }

    res.redirect('/admin/events');
  } catch (error) {
    req.flash('error', 'Error performing bulk action');
    res.redirect('/admin/events');
  }
};

// Upload gallery images for an event
exports.uploadGalleryImages = async (req, res) => {
  console.log('🖼️ Gallery upload request received');
  console.log('Event ID:', req.params.id);
  console.log('Files in request:', req.files ? req.files.length : 'No files');

  try {
    uploadGallery(req, res, async (err) => {
      if (err) {
        console.error('❌ Gallery upload error:', err);
        return res.status(400).json({ success: false, error: err.message });
      }

      console.log('✅ Multer processing completed');
      console.log('Files after multer:', req.files ? req.files.length : 'No files');

      const event = await Event.findById(req.params.id);
      if (!event) {
        console.error('❌ Event not found:', req.params.id);
        return res.status(404).json({ success: false, error: 'Event not found' });
      }

      console.log('✅ Found event:', event.title);

      // Add new gallery images
      if (req.files && req.files.length > 0) {
        const newImages = req.files.map(file => `/uploads/events/${file.filename}`);
        console.log('📸 New images to add:', newImages);

        event.gallery = event.gallery || [];
        event.gallery.push(...newImages);
        await event.save();

        console.log('✅ Gallery updated successfully');
        console.log('Total gallery images:', event.gallery.length);

        res.json({
          success: true,
          message: `${req.files.length} image(s) uploaded successfully`,
          images: newImages,
          totalImages: event.gallery.length
        });
      } else {
        console.log('❌ No files were uploaded');
        res.status(400).json({ success: false, error: 'No images were uploaded' });
      }
    });
  } catch (error) {
    console.error('❌ Error uploading gallery images:', error);
    res.status(500).json({ success: false, error: 'Error uploading images' });
  }
};

// Delete a gallery image
exports.deleteGalleryImage = async (req, res) => {
  try {
    const { eventId, imageIndex } = req.params;

    const event = await Event.findById(eventId);
    if (!event) {
      return res.status(404).json({ success: false, error: 'Event not found' });
    }

    if (!event.gallery || event.gallery.length === 0) {
      return res.status(400).json({ success: false, error: 'No gallery images found' });
    }

    const index = parseInt(imageIndex);
    if (index < 0 || index >= event.gallery.length) {
      return res.status(400).json({ success: false, error: 'Invalid image index' });
    }

    // Remove the image from the gallery array
    event.gallery.splice(index, 1);
    await event.save();

    res.json({
      success: true,
      message: 'Image deleted successfully',
      remainingImages: event.gallery.length
    });
  } catch (error) {
    console.error('Error deleting gallery image:', error);
    res.status(500).json({ success: false, error: 'Error deleting image' });
  }
};
