/* Science Club Website Styles */

/* CSS Variables for Design System */
:root {
  /* Primary Colors */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* Secondary Colors */
  --secondary-50: #f0fdf4;
  --secondary-100: #dcfce7;
  --secondary-200: #bbf7d0;
  --secondary-300: #86efac;
  --secondary-400: #4ade80;
  --secondary-500: #22c55e;
  --secondary-600: #16a34a;
  --secondary-700: #15803d;
  --secondary-800: #166534;
  --secondary-900: #14532d;

  /* Accent Colors */
  --accent-50: #fdf4ff;
  --accent-100: #fae8ff;
  --accent-200: #f5d0fe;
  --accent-300: #f0abfc;
  --accent-400: #e879f9;
  --accent-500: #d946ef;
  --accent-600: #c026d3;
  --accent-700: #a21caf;
  --accent-800: #86198f;
  --accent-900: #701a75;

  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Semantic Colors */
  --success-500: #10b981;
  --warning-500: #f59e0b;
  --error-500: #ef4444;
  --info-500: #3b82f6;

  /* Spacing Scale */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;

  /* Line Heights */
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  overflow-x: hidden;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: var(--leading-normal);
  color: var(--gray-700);
  background-color: var(--gray-50);
  font-size: var(--font-size-base);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  width: 100%;
  max-width: 100vw;
}

/* Container and Layout */
.max-w-7xl { max-width: 1280px; margin: 0 auto; padding-left: 16px; padding-right: 16px; }
.max-w-4xl { max-width: 896px; margin: 0 auto; padding-left: 16px; padding-right: 16px; }
.max-w-3xl { max-width: 768px; margin: 0 auto; padding-left: 16px; padding-right: 16px; }
.max-w-2xl { max-width: 672px; margin: 0 auto; padding-left: 16px; padding-right: 16px; }
.max-w-md { max-width: 448px; margin: 0 auto; padding-left: 16px; padding-right: 16px; }

/* Responsive container adjustments */
@media (min-width: 640px) {
  .max-w-7xl, .max-w-4xl, .max-w-3xl, .max-w-2xl, .max-w-md {
    padding-left: 24px;
    padding-right: 24px;
  }
}

@media (min-width: 1024px) {
  .max-w-7xl, .max-w-4xl, .max-w-3xl, .max-w-2xl, .max-w-md {
    padding-left: 32px;
    padding-right: 32px;
  }
}

/* Spacing */
.px-4 { padding-left: 16px; padding-right: 16px; }
.px-6 { padding-left: 24px; padding-right: 24px; }
.px-8 { padding-left: 32px; padding-right: 32px; }
.py-4 { padding-top: 16px; padding-bottom: 16px; }
.py-6 { padding-top: 24px; padding-bottom: 24px; }
.py-8 { padding-top: 32px; padding-bottom: 32px; }
.py-12 { padding-top: 48px; padding-bottom: 48px; }
.py-16 { padding-top: 64px; padding-bottom: 64px; }
.py-20 { padding-top: 80px; padding-bottom: 80px; }

.mb-2 { margin-bottom: 8px; }
.mb-4 { margin-bottom: 16px; }
.mb-6 { margin-bottom: 24px; }
.mb-8 { margin-bottom: 32px; }
.mb-12 { margin-bottom: 48px; }
.mt-4 { margin-top: 16px; }
.mt-6 { margin-top: 24px; }
.mt-8 { margin-top: 32px; }
.mt-12 { margin-top: 48px; }

/* Grid System */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.gap-4 { gap: 16px; }
.gap-6 { gap: 24px; }
.gap-8 { gap: 32px; }

/* Responsive Grid */
@media (min-width: 768px) {
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .md\:flex-row { flex-direction: row; }
  .md\:text-2xl { font-size: 24px; }
  .md\:text-4xl { font-size: 36px; }
  .md\:text-6xl { font-size: 60px; }
  .md\:block { display: block !important; }
  .md\:flex { display: flex; }
  .md\:hidden { display: none !important; }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\:px-8 { padding-left: 32px; padding-right: 32px; }
  .lg\:block { display: block !important; }
}

@media (min-width: 640px) {
  .sm\:flex-row { flex-direction: row; }
  .sm\:px-6 { padding-left: 24px; padding-right: 24px; }
}

/* Flexbox */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.space-x-4 > * + * { margin-left: 16px; }
.space-x-6 > * + * { margin-left: 24px; }
.space-y-3 > * + * { margin-top: 12px; }
.space-y-4 > * + * { margin-top: 16px; }
.space-y-6 > * + * { margin-top: 24px; }

/* Text Styles */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.text-xs { font-size: 12px; }
.text-sm { font-size: 14px; }
.text-base { font-size: 16px; }
.text-lg { font-size: 18px; }
.text-xl { font-size: 20px; }
.text-2xl { font-size: 24px; }
.text-3xl { font-size: 30px; }
.text-4xl { font-size: 36px; }
.text-5xl { font-size: 48px; }
.text-6xl { font-size: 60px; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* Colors */
.text-white { color: white; }
.text-gray-400 { color: #9ca3af; }
.text-gray-500 { color: #6b7280; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-900 { color: #111827; }
.text-blue-600 { color: #2563eb; }
.text-blue-800 { color: #1e40af; }
.text-green-600 { color: #059669; }
.text-green-800 { color: #065f46; }
.text-red-600 { color: #dc2626; }
.text-red-800 { color: #991b1b; }
.text-yellow-800 { color: #92400e; }
.text-purple-800 { color: #6b21a8; }

/* Background Colors */
.bg-white { background-color: white; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-200 { background-color: #e5e7eb; }
.bg-gray-900 { background-color: #111827; }
.bg-blue-50 { background-color: #eff6ff; }
.bg-blue-100 { background-color: #dbeafe; }
.bg-blue-600 { background-color: #2563eb; }
.bg-green-100 { background-color: #dcfce7; }
.bg-green-600 { background-color: #16a34a; }
.bg-yellow-100 { background-color: #fef3c7; }
.bg-red-100 { background-color: #fee2e2; }
.bg-purple-100 { background-color: #f3e8ff; }

/* Gradients */
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
.from-blue-600 { --tw-gradient-from: #2563eb; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(37, 99, 235, 0)); }
.to-purple-700 { --tw-gradient-to: #7c3aed; }
.to-blue-800 { --tw-gradient-to: #1e40af; }
.from-green-600 { --tw-gradient-from: #059669; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(5, 150, 105, 0)); }
.to-blue-700 { --tw-gradient-to: #1d4ed8; }
.from-purple-600 { --tw-gradient-from: #9333ea; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(147, 51, 234, 0)); }
.from-green-500 { --tw-gradient-from: #10b981; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(16, 185, 129, 0)); }
.to-blue-600 { --tw-gradient-to: #2563eb; }
.from-blue-500 { --tw-gradient-from: #3b82f6; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(59, 130, 246, 0)); }

/* Border and Rounded */
.border { border: 1px solid #e5e7eb; }
.border-b { border-bottom: 1px solid #e5e7eb; }
.border-t { border-top: 1px solid #e5e7eb; }
.border-gray-200 { border-color: #e5e7eb; }
.rounded { border-radius: 4px; }
.rounded-lg { border-radius: 8px; }
.rounded-full { border-radius: 9999px; }

/* Shadow */
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

/* Width and Height */
.w-full { width: 100%; max-width: 100%; }
.w-32 { width: 128px; }
.w-48 { width: 192px; }
.h-48 { height: 192px; }
.h-64 { height: 256px; }
.h-96 { height: 384px; }
.min-h-screen { min-height: 100vh; }

/* Object Fit */
.object-cover { object-fit: cover; }

/* Overflow */
.overflow-hidden { overflow: hidden; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-auto { overflow-y: auto; }

/* Position */
.relative { position: relative; }
.absolute { position: absolute; }
.sticky { position: sticky; }
.top-0 { top: 0; }

/* Z-index */
.z-50 { z-index: 50; }

/* Opacity */
.opacity-90 { opacity: 0.9; }

/* Transitions */
.transition-colors { transition: color 0.2s, background-color 0.2s; }
.transition-shadow { transition: box-shadow 0.3s; }
.transition-all { transition: all 0.2s; }
.duration-200 { transition-duration: 0.2s; }
.duration-300 { transition-duration: 0.3s; }

/* Hover Effects */
.hover\:bg-blue-700:hover { background-color: #1d4ed8; }
.hover\:bg-gray-100:hover { background-color: #f3f4f6; }
.hover\:text-blue-600:hover { color: #2563eb; }
.hover\:text-blue-800:hover { color: #1e40af; }
.hover\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

/* Focus Effects */
.focus\:outline-none:focus { outline: none; }
.focus\:ring-2:focus { box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1); }

/* Display */
.block { display: block; }
.inline-block { display: inline-block; }
.hidden { display: none; }

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  line-height: var(--leading-tight);
  position: relative;
  overflow: hidden;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--font-size-xs);
}

.btn-md {
  padding: var(--space-3) var(--space-4);
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: var(--space-4) var(--space-6);
  font-size: var(--font-size-base);
}

.btn-xl {
  padding: var(--space-5) var(--space-8);
  font-size: var(--font-size-lg);
}

/* Primary Button */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white;
  box-shadow: var(--shadow-sm);
  padding: var(--space-3) var(--space-4);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
  text-decoration: none;
  color: white;
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Secondary Button */
.btn-secondary {
  background: linear-gradient(135deg, var(--gray-600), var(--gray-700));
  color: white;
  box-shadow: var(--shadow-sm);
  padding: var(--space-3) var(--space-4);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, var(--gray-700), var(--gray-800));
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
  text-decoration: none;
  color: white;
}

/* Success Button */
.btn-success {
  background: linear-gradient(135deg, var(--secondary-600), var(--secondary-700));
  color: white;
  box-shadow: var(--shadow-sm);
  padding: var(--space-3) var(--space-4);
}

.btn-success:hover {
  background: linear-gradient(135deg, var(--secondary-700), var(--secondary-800));
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
  text-decoration: none;
  color: white;
}

/* Danger Button */
.btn-danger {
  background: linear-gradient(135deg, var(--error-500), #dc2626);
  color: white;
  box-shadow: var(--shadow-sm);
  padding: var(--space-3) var(--space-4);
}

.btn-danger:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
  text-decoration: none;
  color: white;
}

/* Outline Button */
.btn-outline {
  border: 2px solid var(--primary-600);
  color: var(--primary-600);
  background-color: transparent;
  padding: calc(var(--space-3) - 2px) calc(var(--space-4) - 2px);
  position: relative;
  overflow: hidden;
}

.btn-outline::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  transition: left var(--transition-normal);
  z-index: -1;
}

.btn-outline:hover {
  color: white !important;
  text-decoration: none;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-600);
}

.btn-outline:hover::before {
  left: 0;
}

/* Ghost Button */
.btn-ghost {
  background-color: transparent;
  color: var(--gray-600);
  padding: var(--space-3) var(--space-4);
}

.btn-ghost:hover {
  background-color: var(--gray-100);
  color: var(--gray-700);
  text-decoration: none;
}

/* Button Combinations - Enhanced hover states */
.btn.btn-primary {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
  color: white !important;
  text-decoration: none;
}

.btn.btn-secondary {
  background: linear-gradient(135deg, var(--gray-600), var(--gray-700));
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn.btn-secondary:hover {
  background: linear-gradient(135deg, var(--gray-700), var(--gray-800));
  color: white !important;
  text-decoration: none;
}

.btn.btn-success {
  background: linear-gradient(135deg, var(--secondary-600), var(--secondary-700));
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn.btn-success:hover {
  background: linear-gradient(135deg, var(--secondary-700), var(--secondary-800));
  color: white !important;
  text-decoration: none;
}

.btn.btn-danger {
  background: linear-gradient(135deg, var(--error-500), #dc2626);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn.btn-danger:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white !important;
  text-decoration: none;
}

.btn.btn-outline {
  border: 2px solid var(--primary-600);
  color: var(--primary-600);
  background-color: transparent;
}

/* White border outline buttons for dark backgrounds */
.btn.btn-outline.border-white {
  border-color: white;
  color: white;
}

.btn.btn-outline.border-white:hover {
  background-color: white;
  color: var(--primary-600) !important;
  border-color: white;
}

.btn.btn-outline.border-white:hover::before {
  background: white;
}

/* Ensure proper spacing for icons in buttons */
.btn i {
  display: inline-flex;
  align-items: center;
}

/* Full width buttons */
.btn.w-full {
  width: 100%;
  justify-content: center;
}

/* Newsletter Form Enhancements */
.newsletter-input {
  transition: all 0.3s ease;
  position: relative;
}

.newsletter-input:focus {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.email-suggestion {
  animation: slideInUp 0.3s ease;
}

.email-error {
  animation: shake 0.5s ease;
}

.newsletter-success {
  animation: fadeInUp 0.5s ease;
}

/* Newsletter form specific styling */
form[data-newsletter] {
  max-width: 400px;
}

form[data-newsletter] input[type="email"] {
  min-width: 200px;
  flex: 1;
}

form[data-newsletter] button {
  flex-shrink: 0;
  min-width: auto;
}

/* Responsive newsletter forms */
@media (max-width: 640px) {
  form[data-newsletter] {
    max-width: 100%;
  }

  form[data-newsletter] .flex-col {
    gap: 12px;
  }

  form[data-newsletter] button {
    width: 100%;
    justify-content: center;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced input focus states */
input[type="email"]:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Floating placeholder effect */
.floating-placeholder {
  position: relative;
}

.floating-placeholder input:focus + label,
.floating-placeholder input:not(:placeholder-shown) + label {
  transform: translateY(-1.5rem) scale(0.8);
  color: var(--primary-600);
}

/* Form Styles */
.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* CRITICAL FIX: Ensure icons are positioned INSIDE input field boundaries */
.relative .absolute.inset-y-0 {
  top: 1px !important;
  bottom: 1px !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.relative .absolute.inset-y-0.left-0 {
  left: 1px !important;
  width: 2.5rem;
  padding: 0;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.relative .absolute.inset-y-0.right-0 {
  right: 1px !important;
  width: 2.5rem;
  padding: 0;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

/* Ensure icons are centered within their containers */
.relative .absolute.inset-y-0 i {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #9ca3af;
  pointer-events: none;
}

/* Input padding adjustments for icons */
input.pl-10 {
  padding-left: 2.5rem !important;
}

input.pl-12 {
  padding-left: 3rem !important;
}

input.pr-10 {
  padding-right: 2.5rem !important;
}

input.pr-12 {
  padding-right: 3rem !important;
}

/* Specific fixes for search inputs */
.relative input[type="text"] {
  position: relative;
  z-index: 1;
}

.relative input[type="email"] {
  position: relative;
  z-index: 1;
}

/* Override any conflicting padding from other classes */
.relative .absolute.inset-y-0.left-0.pl-3 {
  padding-left: 0 !important;
  left: 1px !important;
  width: 2.5rem !important;
}

.relative .absolute.inset-y-0.right-0.pr-3 {
  padding-right: 0 !important;
  right: 1px !important;
  width: 2.5rem !important;
}

/* Handle larger input fields (like on search page) */
.relative input.text-lg {
  padding-left: 3rem !important;
}

.relative input.text-lg + .absolute.left-0 {
  width: 3rem !important;
}

.relative input.text-lg + .absolute.left-0 i {
  font-size: 1.125rem;
}

/* Ensure proper border radius for icon containers */
.relative .absolute.inset-y-0.left-0 {
  border-top-left-radius: inherit;
  border-bottom-left-radius: inherit;
}

.relative .absolute.inset-y-0.right-0 {
  border-top-right-radius: inherit;
  border-bottom-right-radius: inherit;
}

/* Fix for rounded inputs */
.relative input.rounded-lg + .absolute.left-0 {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.relative input.rounded-lg + .absolute.right-0 {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}

.relative input.rounded-xl + .absolute.left-0 {
  border-top-left-radius: 12px;
  border-bottom-left-radius: 12px;
}

.relative input.rounded-xl + .absolute.right-0 {
  border-top-right-radius: 12px;
  border-bottom-right-radius: 12px;
}

.form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  font-size: 14px;
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-checkbox {
  height: 16px;
  width: 16px;
  color: #2563eb;
  border: 1px solid #d1d5db;
  border-radius: 4px;
}

/* Card Styles */
.card {
  background-color: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);
  position: relative;
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--accent-500));
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.card:hover::before {
  opacity: 1;
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
  background: linear-gradient(135deg, var(--gray-50), white);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--gray-200);
  background: linear-gradient(135deg, white, var(--gray-50));
}

/* Enhanced Card Variants */
.card-elevated {
  box-shadow: var(--shadow-lg);
  border: none;
}

.card-elevated:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
}

.card-interactive {
  cursor: pointer;
  transition: all var(--transition-normal);
}

.card-interactive:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-3px) scale(1.02);
}

.card-gradient {
  background: linear-gradient(135deg, white, var(--primary-50));
  border: 1px solid var(--primary-200);
}

.card-glass {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Alert Styles */
.alert {
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.alert-success {
  background-color: #dcfce7;
  border: 1px solid #86efac;
  color: #15803d;
}

.alert-error {
  background-color: #fee2e2;
  border: 1px solid #fca5a5;
  color: #dc2626;
}

.alert-warning {
  background-color: #fef3c7;
  border: 1px solid #fcd34d;
  color: #d97706;
}

.alert-info {
  background-color: #dbeafe;
  border: 1px solid #93c5fd;
  color: #2563eb;
}

/* Navigation Styles */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-sm);
  border-bottom: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
}

.navbar.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-lg);
}

/* Ultra Modern Animated Hamburger Menu */
.modern-menu-toggle {
  z-index: 1001;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  /* Ensure the entire button is clickable */
  -webkit-tap-highlight-color: transparent;
  user-select: none;
}

.modern-menu-toggle:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.modern-menu-toggle:active {
  transform: scale(0.95);
}

/* Hamburger Icon Container */
.hamburger-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
  visibility: visible;
}

.hamburger-line {
  display: block;
  width: 20px;
  height: 2px;
  background: linear-gradient(135deg, #374151, #4b5563);
  border-radius: 2px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
  position: relative;
  pointer-events: auto; /* Enable clicks on hamburger lines */
}

.hamburger-line.top-line {
  margin-bottom: 4px;
  transform: translateY(0);
}

.hamburger-line.middle-line {
  margin-bottom: 4px;
  transform: scaleX(1);
}

.hamburger-line.bottom-line {
  transform: translateY(0);
}

/* X Icon Container */
.x-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(180deg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  visibility: hidden;
  pointer-events: none; /* Ensure clicks pass through when hidden */
}

.x-line {
  display: block;
  width: 20px;
  height: 2px;
  background: linear-gradient(135deg, #dc2626, #ef4444);
  border-radius: 2px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform-origin: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none; /* Ensure clicks pass through */
}

.x-line.line-1 {
  transform: translate(-50%, -50%) rotate(45deg);
}

.x-line.line-2 {
  transform: translate(-50%, -50%) rotate(-45deg);
}

/* Active State - Show X, Hide Hamburger */
.modern-menu-toggle.active .hamburger-icon {
  opacity: 0;
  visibility: hidden;
  transform: translate(-50%, -50%) rotate(-180deg) scale(0.8);
  pointer-events: none; /* Disable clicks when hidden */
}

.modern-menu-toggle.active .hamburger-line {
  pointer-events: none; /* Disable clicks on hamburger lines when hidden */
}

.modern-menu-toggle.active .x-icon {
  opacity: 1;
  visibility: visible;
  transform: translate(-50%, -50%) rotate(0deg);
  pointer-events: auto; /* Enable clicks when visible */
}

.modern-menu-toggle.active .x-line {
  pointer-events: auto; /* Enable clicks on X lines when visible */
}

/* Hover effects for individual lines */
.modern-menu-toggle:hover .hamburger-line.top-line {
  transform: translateY(-1px);
}

.modern-menu-toggle:hover .hamburger-line.bottom-line {
  transform: translateY(1px);
}

.modern-menu-toggle:hover .hamburger-line.middle-line {
  transform: scaleX(0.8);
}

/* Modern Mobile Search Styling */
.search-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.search-icon-container::before {
  content: '';
  position: absolute;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.1));
  border-radius: 50%;
  transform: scale(0);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.group:focus-within .search-icon-container::before {
  transform: scale(1);
}

/* Mobile search input enhancements */
#mobile-search-input {
  font-size: 16px; /* Prevents zoom on iOS */
}

#mobile-search-input:focus {
  transform: translateY(-1px);
}

/* Quick search hints animation */
.mobile-menu-content .flex-wrap span {
  transition: all 0.2s ease;
  cursor: pointer;
}

.mobile-menu-content .flex-wrap span:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
  transform: translateY(-1px);
  border-color: #93c5fd;
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all var(--transition-normal);
}

.mobile-menu-overlay.active {
  visibility: visible;
  opacity: 1;
}

.mobile-menu-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  transition: all var(--transition-normal);
}

.mobile-menu-panel {
  position: absolute;
  top: 0;
  right: 0;
  width: 320px;
  max-width: 85vw;
  height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  backdrop-filter: blur(20px);
  box-shadow: -10px 0 50px rgba(0, 0, 0, 0.15);
  border-left: 1px solid rgba(255, 255, 255, 0.2);
  transform: translateX(100%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.mobile-menu-overlay.active .mobile-menu-panel {
  transform: translateX(0);
}

.mobile-menu-header {
  border-bottom: 1px solid var(--gray-200);
  background: linear-gradient(135deg, var(--primary-50), white);
}

.mobile-menu-close {
  padding: 8px;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.mobile-menu-close:hover {
  background-color: var(--gray-100);
  color: var(--primary-600);
}

.mobile-menu-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.mobile-nav-links {
  flex: 1;
  padding: 16px 0;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  padding: 18px 24px;
  margin: 4px 16px;
  color: var(--gray-700);
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  border-left: 4px solid transparent;
  position: relative;
  overflow: hidden;
}

.mobile-nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(99, 102, 241, 0.05));
  transform: translateX(-100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 0;
}

.mobile-nav-link:hover::before {
  transform: translateX(0);
}

.mobile-nav-link:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(99, 102, 241, 0.08));
  color: var(--primary-600);
  border-left-color: var(--primary-500);
  text-decoration: none;
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.mobile-nav-link.active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.12), rgba(99, 102, 241, 0.12));
  color: var(--primary-700);
  border-left-color: var(--primary-600);
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.mobile-nav-link > * {
  position: relative;
  z-index: 1;
}

.mobile-nav-link i:first-child {
  width: 20px;
  margin-right: 16px;
  text-align: center;
}

.mobile-nav-link span {
  flex: 1;
}

.mobile-nav-link i:last-child {
  opacity: 0.5;
  transition: all var(--transition-fast);
}

.mobile-nav-link:hover i:last-child {
  opacity: 1;
  transform: translateX(4px);
}

.navbar-brand {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-600), var(--accent-600));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-decoration: none;
  transition: all var(--transition-normal);
}

.navbar-brand:hover {
  text-decoration: none;
  transform: scale(1.05);
}

/* Navbar navigation - styles defined below in enhanced section */

.nav-link {
  color: var(--gray-700);
  text-decoration: none;
  font-weight: 500;
  transition: all var(--transition-normal);
  position: relative;
  padding: var(--space-2) var(--space-2);
  border-radius: var(--radius-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 60px;
  font-size: var(--font-size-xs);
  line-height: 1.2;
}

.nav-link::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-500), var(--accent-500));
  transition: all var(--transition-normal);
  transform: translateX(-50%);
}

.nav-link:hover {
  color: var(--primary-600);
  text-decoration: none;
  background-color: var(--primary-50);
}

.nav-link:hover::before {
  width: 80%;
}

.nav-link.active {
  color: var(--primary-600);
  background-color: var(--primary-50);
  border: 1px solid var(--primary-200);
}

.nav-link.active::before {
  width: 80%;
}

/* Enhanced Desktop Navigation with Dropdowns */
.nav-dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-trigger {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.dropdown-trigger .fa-chevron-down {
  transition: transform var(--transition-fast);
  opacity: 0.7;
}

.nav-dropdown:hover .dropdown-trigger .fa-chevron-down {
  transform: rotate(180deg);
  opacity: 1;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--gray-200);
  min-width: 280px;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-50%) translateY(-10px);
  transition: all var(--transition-normal);
  z-index: 1000;
  margin-top: 8px;
}

.nav-dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}

.dropdown-content {
  padding: var(--space-2);
}

.dropdown-item {
  display: flex;
  align-items: flex-start;
  padding: var(--space-3) var(--space-4);
  color: var(--gray-700);
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  gap: var(--space-3);
}

.dropdown-item:hover {
  background-color: var(--primary-50);
  color: var(--primary-700);
  text-decoration: none;
  transform: translateX(2px);
}

.dropdown-item i {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  background-color: var(--gray-100);
  font-size: 12px;
  margin-top: 2px;
  transition: all var(--transition-fast);
}

.dropdown-item:hover i {
  background-color: white;
  transform: scale(1.1);
}

.dropdown-title {
  display: block;
  font-weight: 600;
  font-size: var(--font-size-sm);
  color: var(--gray-900);
  margin-bottom: 2px;
}

.dropdown-desc {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  line-height: var(--leading-tight);
}

.dropdown-item:hover .dropdown-title {
  color: var(--primary-700);
}

.dropdown-item:hover .dropdown-desc {
  color: var(--primary-600);
}

/* Dropdown Animation Enhancement */
.dropdown-menu::before {
  content: '';
  position: absolute;
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 12px;
  height: 12px;
  background: white;
  border: 1px solid var(--gray-200);
  border-bottom: none;
  border-right: none;
  transform: translateX(-50%) rotate(45deg);
}

/* Enhanced navbar spacing for dropdowns */
.navbar-nav {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex-wrap: nowrap;
}

/* Improved hover states for dropdown triggers */
.dropdown-trigger:hover {
  color: var(--primary-600);
  background-color: var(--primary-50);
}

.dropdown-trigger.active {
  color: var(--primary-600);
  background-color: var(--primary-50);
  border: 1px solid var(--primary-200);
}

/* Responsive adjustments for dropdowns */
@media (max-width: 1024px) {
  .dropdown-menu {
    min-width: 240px;
  }

  .navbar-nav {
    gap: var(--space-2);
  }

  /* Adjust search bar width on smaller screens */
  #search-input {
    width: 220px !important;
  }
}

/* Additional responsive adjustments for medium screens */
@media (max-width: 1200px) {
  .navbar-nav {
    gap: var(--space-2);
  }

  #search-input {
    width: 240px !important;
  }
}

/* Active dropdown state */
.nav-dropdown.active .dropdown-trigger {
  color: var(--primary-600);
  background-color: var(--primary-50);
}

.nav-dropdown.active .dropdown-trigger .fa-chevron-down {
  transform: rotate(180deg);
  opacity: 1;
}

/* Dropdown item focus states for keyboard navigation */
.dropdown-item:focus {
  outline: none;
  background-color: var(--primary-100);
  color: var(--primary-700);
  transform: translateX(2px);
}

/* Enhanced dropdown animations */
.dropdown-menu {
  animation-duration: 0.2s;
  animation-timing-function: ease-out;
  animation-fill-mode: both;
}

.nav-dropdown:hover .dropdown-menu {
  animation-name: dropdownFadeIn;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
  }
}

/* Improved dropdown positioning for edge cases */
.nav-dropdown:first-child .dropdown-menu {
  left: 0;
  transform: translateX(0);
}

.nav-dropdown:first-child .dropdown-menu::before {
  left: 20px;
  transform: translateX(0) rotate(45deg);
}

.nav-dropdown:last-child .dropdown-menu {
  right: 0;
  left: auto;
  transform: translateX(0);
}

.nav-dropdown:last-child .dropdown-menu::before {
  right: 20px;
  left: auto;
  transform: translateX(0) rotate(45deg);
}

/* Dropdown hover delay for better UX */
.nav-dropdown .dropdown-menu {
  transition-delay: 0ms;
}

.nav-dropdown:hover .dropdown-menu {
  transition-delay: 100ms;
}

@media (max-width: 768px) {
  .nav-dropdown {
    display: none; /* Hide dropdowns on mobile, use mobile menu instead */
  }
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-800), var(--accent-600));
  color: white;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.hero-content {
  position: relative;
  z-index: 1;
}

.hero-title {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  margin-bottom: var(--space-4);
  line-height: var(--leading-tight);
  background: linear-gradient(135deg, white, rgba(255, 255, 255, 0.8));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-8);
  opacity: 0.9;
  line-height: var(--leading-relaxed);
}

.hero-cta {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
  justify-content: center;
}

@media (min-width: 768px) {
  .hero-title {
    font-size: var(--font-size-6xl);
  }
  .hero-subtitle {
    font-size: var(--font-size-2xl);
  }
  .hero-cta {
    justify-content: flex-start;
  }
}

/* Section Styles */
.section {
  padding-top: 64px;
  padding-bottom: 64px;
}

.section-title {
  font-size: 30px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 48px;
  color: #111827;
}

.section-subtitle {
  font-size: 18px;
  color: #6b7280;
  text-align: center;
  margin-bottom: 48px;
  max-width: 768px;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 768px) {
  .section-title {
    font-size: 36px;
  }
}

/* Project Card Styles */
.project-card {
  background-color: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.project-card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
}

.project-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.project-card:hover::before {
  opacity: 1;
}

.project-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.project-card:hover .project-image {
  transform: scale(1.05);
}

.project-content {
  padding: var(--space-6);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.project-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: var(--space-2);
  color: var(--gray-900);
  line-height: var(--leading-tight);
}

.project-description {
  color: var(--gray-600);
  margin-bottom: var(--space-4);
  line-height: var(--leading-relaxed);
  flex: 1;
}

.project-authors {
  font-size: var(--font-size-sm);
  color: var(--primary-600);
  margin-bottom: var(--space-3);
  font-weight: 500;
}





/* Blog Card Styles */
.blog-card {
  background-color: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.blog-card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
}

.blog-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-500), var(--secondary-500));
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.blog-card:hover::before {
  opacity: 1;
}

.blog-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.blog-card:hover .blog-image {
  transform: scale(1.05);
}

.blog-content {
  padding: var(--space-6);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.blog-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: var(--space-2);
  color: var(--gray-900);
  line-height: var(--leading-tight);
}

.blog-excerpt {
  color: var(--gray-600);
  margin-bottom: var(--space-4);
  line-height: var(--leading-relaxed);
  flex: 1;
}

.blog-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--gray-500);
  padding-top: var(--space-4);
  border-top: 1px solid var(--gray-200);
}

.blog-author {
  font-weight: 500;
  color: var(--primary-600);
}

.blog-date {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.categories, .tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.category, .tag {
  background: linear-gradient(135deg, var(--secondary-100), var(--secondary-200));
  color: var(--secondary-700);
  font-size: var(--font-size-xs);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-3xl);
  font-weight: 500;
  border: 1px solid var(--secondary-300);
  transition: all var(--transition-fast);
}
.tag:hover, .category:hover {
  background: linear-gradient(135deg, var(--secondary-200), var(--secondary-300));
  transform: translateY(-1px);
}

.fea{
      background-color: darkgray;
    color: white;
}


/* Event Card Styles */
.event-card {
  background-color: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.event-card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
}

.event-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--secondary-500), var(--accent-500));
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.event-card:hover::before {
  opacity: 1;
}

.event-date {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white;
  padding: var(--space-4);
  text-align: center;
  position: relative;
}

.event-date::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid var(--primary-700);
}

.event-day {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  line-height: var(--leading-tight);
}

.event-month {
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  opacity: 0.9;
}

.event-content {
  padding: var(--space-6);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.event-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: var(--space-2);
  color: var(--gray-900);
  line-height: var(--leading-tight);
}

.event-description {
  color: var(--gray-600);
  margin-bottom: var(--space-4);
  line-height: var(--leading-relaxed);
  flex: 1;
}

.event-location {
  font-size: var(--font-size-sm);
  color: var(--primary-600);
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-weight: 500;
  padding: var(--space-2) var(--space-3);
  background-color: var(--primary-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--primary-200);
}

/* Resource Card Styles */
.resource-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 24px;
  transition: box-shadow 0.3s;
}

.resource-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.resource-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
}

.resource-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #111827;
}

.resource-description {
  color: #6b7280;
  margin-bottom: 16px;
}

.resource-type {
  display: inline-block;
  background-color: #f3e8ff;
  color: #6b21a8;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 9999px;
  text-transform: uppercase;
}

/* Footer Styles */
.footer {
  background-color: #111827;
  color: white;
}

.footer-content {
  padding-top: 48px;
  padding-bottom: 48px;
}

.footer-section {
  margin-bottom: 32px;
}

.footer-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
}

.footer-link {
  color: #d1d5db;
  text-decoration: none;
  transition: color 0.2s;
}

.footer-link:hover {
  color: white;
  text-decoration: none;
}

.footer-bottom {
  border-top: 1px solid #374151;
  padding-top: 24px;
  padding-bottom: 24px;
  text-align: center;
  color: #9ca3af;
}

/* Admin Styles */
.admin-sidebar {
  background-color: #111827;
  color: white;
  width: 256px;
  min-height: 100vh;
}

.admin-nav {
  padding-top: 16px;
  padding-bottom: 16px;
}

.admin-nav-item {
  display: block;
  padding: 12px 24px;
  color: #d1d5db;
  text-decoration: none;
  transition: all 0.2s;
}

.admin-nav-item:hover {
  background-color: #374151;
  color: white;
  text-decoration: none;
}

.admin-nav-item.active {
  background-color: #2563eb;
  color: white;
}

.admin-content {
  flex: 1;
  padding: 24px;
}

.admin-header {
  margin-bottom: 24px;
}

.admin-title {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
}

/* Enhanced Admin Styles */
.admin-sidebar {
  position: relative;
  z-index: 10;
}

.admin-nav-item {
  position: relative;
  overflow: hidden;
}

.admin-nav-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background-color: #2563eb;
  transform: scaleY(0);
  transition: transform 0.2s ease;
}

.admin-nav-item.active::before {
  transform: scaleY(1);
}

/* Dropdown styles */
.dropdown-panel {
  transform: translateY(-10px);
  opacity: 0;
  transition: all 0.2s ease;
  pointer-events: none;
}

.dropdown-panel:not(.hidden) {
  transform: translateY(0);
  opacity: 1;
  pointer-events: auto;
}

/* Stats card hover effects */
.stats-card {
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
}

/* Notification badge animation */
.notification-badge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Table enhancements */
.admin-table {
  border-collapse: separate;
  border-spacing: 0;
}

.admin-table th {
  background-color: #f9fafb;
  border-bottom: 2px solid #e5e7eb;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  color: #6b7280;
}

.admin-table td {
  border-bottom: 1px solid #f3f4f6;
}

.admin-table tr:hover td {
  background-color: #f9fafb;
}

/* Form enhancements */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-textarea {
  min-height: 120px;
  resize: vertical;
}

/* Status badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.active {
  background-color: #dcfce7;
  color: #166534;
}

.status-badge.inactive {
  background-color: #fee2e2;
  color: #991b1b;
}

.status-badge.draft {
  background-color: #fef3c7;
  color: #92400e;
}

/* Action buttons */
.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
}

.action-btn:hover {
  transform: scale(1.05);
}

.action-btn.edit {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.action-btn.edit:hover {
  background-color: #bfdbfe;
}

.action-btn.delete {
  background-color: #fee2e2;
  color: #dc2626;
}

.action-btn.delete:hover {
  background-color: #fecaca;
}

.action-btn.view {
  background-color: #f0fdf4;
  color: #166534;
}

.action-btn.view:hover {
  background-color: #dcfce7;
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Mobile responsiveness for admin */
@media (max-width: 768px) {
  .admin-sidebar {
    width: 100%;
    height: auto;
    position: fixed;
    top: 0;
    left: -100%;
    transition: left 0.3s ease;
    z-index: 50;
  }

  .admin-sidebar.open {
    left: 0;
  }

  .admin-content {
    padding: 1rem;
  }

  .admin-title {
    font-size: 1.25rem;
  }
}

/* Table Styles */
.table {
  width: 100%;
  border-collapse: collapse;
}

.table th {
  background-color: #f9fafb;
  padding: 12px 24px;
  text-align: left;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid #e5e7eb;
}

.table td {
  padding: 16px 24px;
  white-space: nowrap;
  font-size: 14px;
  color: #111827;
  border-bottom: 1px solid #e5e7eb;
}

.table tbody tr:hover {
  background-color: #f9fafb;
}

/* Utility Classes */
.flex-1 { flex: 1; }
.flex-shrink-0 { flex-shrink: 0; }
.whitespace-nowrap { white-space: nowrap; }
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Enhanced Responsive Design */
/* Extra small devices (320px and up) */
@media (max-width: 320px) {
  body {
    font-size: 14px;
  }

  .hero-title {
    font-size: 20px;
    line-height: 1.1;
    margin-bottom: 12px;
  }

  .hero-subtitle {
    font-size: 12px;
    line-height: 1.3;
  }

  .section-title {
    font-size: 18px;
  }

  .btn {
    padding: 10px 14px;
    font-size: 12px;
  }

  .btn-lg {
    padding: 12px 18px;
    font-size: 14px;
  }

  .px-4 {
    padding-left: 12px;
    padding-right: 12px;
  }

  .py-16 {
    padding-top: 24px;
    padding-bottom: 24px;
  }

  .py-12 {
    padding-top: 18px;
    padding-bottom: 18px;
  }

  .py-20 {
    padding-top: 32px;
    padding-bottom: 32px;
  }
}

/* Small mobile devices (480px and down) */
@media (max-width: 480px) {
  .hero-title {
    font-size: 24px;
    line-height: 1.2;
  }
  .hero-subtitle {
    font-size: 14px;
    line-height: 1.4;
  }
  .section-title {
    font-size: 20px;
  }
  .btn {
    padding: 12px 16px;
    font-size: 14px;
  }
  .btn-lg {
    padding: 14px 20px;
    font-size: 16px;
  }
  .px-4 {
    padding-left: 16px;
    padding-right: 16px;
  }
  .py-16 {
    padding-top: 32px;
    padding-bottom: 32px;
  }
  .py-12 {
    padding-top: 24px;
    padding-bottom: 24px;
  }

  /* Ensure all images are responsive */
  img {
    max-width: 100%;
    height: auto;
  }

  /* Fix any potential overflow issues */
  .w-full {
    width: 100%;
    max-width: 100%;
  }

  /* Improve grid spacing */
  .grid {
    gap: 12px;
  }

  /* Better text sizing */
  .text-4xl {
    font-size: 24px;
  }

  .text-3xl {
    font-size: 20px;
  }

  .text-2xl {
    font-size: 18px;
  }

  .text-xl {
    font-size: 16px;
  }
}

@media (max-width: 767px) {
  .hero-title {
    font-size: 28px;
    line-height: 1.3;
  }
  .hero-subtitle {
    font-size: 16px;
    line-height: 1.5;
  }
  .section-title {
    font-size: 24px;
  }
  .navbar-nav {
    flex-direction: column;
    gap: 8px;
  }
  .grid-3 {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  .hero-cta {
    flex-direction: column;
    gap: 12px;
  }
  .hero-cta .btn {
    width: 100%;
    justify-content: center;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .hero-title {
    font-size: 36px;
  }
  .hero-subtitle {
    font-size: 18px;
  }
  .section-title {
    font-size: 28px;
  }
}















/* Enhanced Mobile Responsiveness */
@media (max-width: 640px) {
  .mobile-menu-panel {
    width: 100vw;
    max-width: 100vw;
  }

  .hero-cta {
    padding: 0 16px;
  }

  .section-title {
    text-align: center;
  }

  .btn-lg {
    padding: 16px 24px;
    font-size: 16px;
  }

  .form-input {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Specific improvements for about page and images */
  .grid.grid-cols-1.lg\\:grid-cols-2 {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  /* Ensure proper image responsiveness */
  .rounded-lg.shadow-lg.w-full.h-auto.object-cover {
    width: 100%;
    height: auto;
    max-width: 100%;
    object-fit: cover;
    border-radius: 8px;
  }

  /* Fix any section overflow */
  section {
    overflow-x: hidden;
    width: 100%;
    max-width: 100vw;
  }

  /* Improve grid layouts */
  .grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3 {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .grid.grid-cols-2.md\\:grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  /* Better spacing for mobile */
  .py-20 {
    padding-top: 48px;
    padding-bottom: 48px;
  }

  .py-16 {
    padding-top: 40px;
    padding-bottom: 40px;
  }
}

/* Print Styles */
@media print {
  .navbar,
  .mobile-menu-overlay,
  .btn,
  .footer {
    display: none !important;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }

  .hero {
    background: none !important;
    color: black !important;
  }
}

/* Loading spinner */
.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #d1d5db;
  border-top: 2px solid #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Additional grid utilities */
.grid-1 { display: grid; grid-template-columns: repeat(1, minmax(0, 1fr)); gap: 24px; }
.grid-2 { display: grid; grid-template-columns: repeat(2, minmax(0, 1fr)); gap: 24px; }
.grid-3 { display: grid; grid-template-columns: repeat(1, minmax(0, 1fr)); gap: 24px; }
.grid-4 { display: grid; grid-template-columns: repeat(4, minmax(0, 1fr)); gap: 24px; }

/* Responsive grid utilities */
@media (min-width: 768px) {
  .grid-3 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
  .grid-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

/* Additional spacing */
.p-2 { padding: 8px; }
.p-3 { padding: 12px; }
.p-4 { padding: 16px; }
.p-6 { padding: 24px; }
.p-8 { padding: 32px; }
.p-12 { padding: 48px; }

.m-2 { margin: 8px; }
.m-3 { margin: 12px; }
.m-4 { margin: 16px; }
.m-6 { margin: 24px; }
.m-8 { margin: 32px; }
.m-12 { margin: 48px; }

.mr-1 { margin-right: 4px; }
.mr-2 { margin-right: 8px; }
.mr-3 { margin-right: 12px; }
.mr-4 { margin-right: 16px; }
.ml-1 { margin-left: 4px; }
.ml-2 { margin-left: 8px; }
.ml-3 { margin-left: 12px; }
.ml-4 { margin-left: 16px; }

/* Width utilities */
.w-8 { width: 32px; }
.w-10 { width: 40px; }
.w-12 { width: 48px; }
.w-16 { width: 64px; }
.w-20 { width: 80px; }
.w-24 { width: 96px; }
.w-64 { width: 256px; }

/* Height utilities */
.h-8 { height: 32px; }
.h-10 { height: 40px; }
.h-12 { height: 48px; }
.h-16 { height: 64px; }
.h-20 { height: 80px; }
.h-24 { height: 96px; }
.h-32 { height: 128px; }

/* Additional colors */
.text-blue-300 { color: #93c5fd; }
.text-blue-400 { color: #60a5fa; }
.text-blue-500 { color: #3b82f6; }
.text-blue-700 { color: #1d4ed8; }
.text-blue-900 { color: #1e3a8a; }

.bg-blue-300 { background-color: #93c5fd; }
.bg-blue-400 { background-color: #60a5fa; }
.bg-blue-500 { background-color: #3b82f6; }
.bg-blue-700 { background-color: #1d4ed8; }
.bg-blue-800 { background-color: #1e40af; }
.bg-blue-900 { background-color: #1e3a8a; }

.bg-green-50 { background-color: #f0fdf4; }
.bg-green-500 { background-color: #22c55e; }
.bg-green-700 { background-color: #15803d; }

.bg-red-50 { background-color: #fef2f2; }
.bg-red-500 { background-color: #ef4444; }
.bg-red-600 { background-color: #dc2626; }

.bg-yellow-50 { background-color: #fefce8; }
.bg-yellow-400 { background-color: #facc15; }
.bg-yellow-500 { background-color: #eab308; }

.bg-purple-50 { background-color: #faf5ff; }
.bg-purple-500 { background-color: #a855f7; }
.bg-purple-600 { background-color: #9333ea; }

.bg-indigo-100 { background-color: #e0e7ff; }
.bg-indigo-600 { background-color: #4f46e5; }

/* Global Mobile Improvements */
@media (max-width: 768px) {
  /* Ensure all containers are properly constrained */
  .container, .max-w-7xl, .max-w-4xl, .max-w-3xl, .max-w-2xl, .max-w-md {
    width: 100%;
    max-width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: 16px;
    padding-right: 16px;
  }

  /* Fix image responsiveness globally */
  img {
    max-width: 100%;
    height: auto;
    display: block;
  }

  /* Improve flex layouts for mobile */
  .flex.flex-col.sm\\:flex-row {
    flex-direction: column;
    gap: 12px;
  }

  /* Better button layouts */
  .flex.flex-col.sm\\:flex-row.gap-4 .btn {
    width: 100%;
    justify-content: center;
  }

  /* Improve card layouts */
  .bg-white.p-8.rounded-lg.shadow-md {
    padding: 16px;
    margin-bottom: 16px;
  }

  /* Fix hero sections */
  .bg-gradient-to-r.from-blue-600.to-purple-700 {
    padding-left: 16px;
    padding-right: 16px;
  }

  /* Improve text readability */
  .text-lg {
    font-size: 16px;
    line-height: 1.5;
  }

  .text-xl {
    font-size: 18px;
    line-height: 1.4;
  }
}

/* Specific About Page Mobile Improvements */
@media (max-width: 768px) {
  /* About page mission section */
  .grid.grid-cols-1.lg\\:grid-cols-2.gap-12 {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  /* About page image container */
  .relative.w-full img {
    width: 100%;
    height: auto;
    max-height: 300px;
    object-fit: cover;
    border-radius: 8px;
  }

  /* About page values section */
  .grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3.gap-8 {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  /* About page team section */
  .grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3.gap-8 .text-center {
    margin-bottom: 24px;
  }

  /* About page statistics */
  .grid.grid-cols-2.md\\:grid-cols-4.gap-8 {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    text-align: center;
  }
}

/* Universal Image Responsiveness */
img {
  max-width: 100%;
  height: auto;
}

/* Ensure no horizontal overflow on any element */
* {
  max-width: 100%;
}

/* Fix for specific image classes */
.w-full.h-auto.object-cover {
  width: 100%;
  height: auto;
  max-width: 100%;
}

/* Additional mobile-first improvements */
@media (max-width: 576px) {
  /* Very small screens */
  .text-4xl {
    font-size: 1.75rem;
  }

  .text-3xl {
    font-size: 1.5rem;
  }

  .text-2xl {
    font-size: 1.25rem;
  }

  .text-xl {
    font-size: 1.125rem;
  }

  .text-lg {
    font-size: 1rem;
  }

  /* Improve padding for very small screens */
  .p-8 {
    padding: 1rem;
  }

  .py-16 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .py-12 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  /* Better button sizing */
  .btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
  }

  /* Improve grid gaps */
  .gap-8 {
    gap: 1rem;
  }

  .gap-6 {
    gap: 0.75rem;
  }

  .gap-4 {
    gap: 0.5rem;
  }
}

/* Blog Page Specific Styles */
.blog-hero-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.blog-content {
  line-height: 1.8;
}

.blog-content p {
  margin-bottom: 1.5rem;
}

.blog-content h1,
.blog-content h2,
.blog-content h3,
.blog-content h4,
.blog-content h5,
.blog-content h6 {
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-weight: 600;
  color: #1f2937;
}

.blog-content h1 { font-size: 2rem; }
.blog-content h2 { font-size: 1.75rem; }
.blog-content h3 { font-size: 1.5rem; }
.blog-content h4 { font-size: 1.25rem; }

.blog-content ul,
.blog-content ol {
  margin-bottom: 1.5rem;
  padding-left: 2rem;
}

.blog-content li {
  margin-bottom: 0.5rem;
}

.blog-content blockquote {
  border-left: 4px solid #6366f1;
  padding-left: 1.5rem;
  margin: 2rem 0;
  font-style: italic;
  color: #4b5563;
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 0.5rem;
}

.blog-content code {
  background: #f1f5f9;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  color: #dc2626;
}

.blog-content pre {
  background: #1e293b;
  color: #e2e8f0;
  padding: 1.5rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1.5rem 0;
}

.blog-content pre code {
  background: transparent;
  color: inherit;
  padding: 0;
}

.blog-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1.5rem 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.blog-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
}

.blog-content th,
.blog-content td {
  border: 1px solid #e5e7eb;
  padding: 0.75rem;
  text-align: left;
}

.blog-content th {
  background: #f9fafb;
  font-weight: 600;
}

.featured-article-badge {
  animation: glow 2s infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(255, 193, 7, 0.5);
  }
  to {
    box-shadow: 0 0 30px rgba(255, 193, 7, 0.8);
  }
}

.article-tag {
  transition: all 0.3s ease;
  cursor: pointer;
}

.article-tag:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Reading Progress Bar */
#reading-progress {
  background: linear-gradient(90deg, #8b5cf6, #ec4899);
  height: 3px;
  z-index: 9999;
}

/* Enhanced Mobile Responsiveness for Blog Pages */
@media (max-width: 640px) {
  .blog-hero-title {
    font-size: 1.875rem;
    line-height: 1.2;
  }

  .blog-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .blog-actions {
    flex-direction: column;
    width: 100%;
  }

  .blog-actions .btn {
    width: 100%;
    justify-content: center;
  }

  .blog-content {
    font-size: 1rem;
    line-height: 1.7;
  }

  .blog-content h1 { font-size: 1.5rem; }
  .blog-content h2 { font-size: 1.375rem; }
  .blog-content h3 { font-size: 1.25rem; }
  .blog-content h4 { font-size: 1.125rem; }

  .blog-content blockquote {
    padding: 1rem;
    margin: 1.5rem 0;
  }

  .blog-content pre {
    padding: 1rem;
    font-size: 0.875rem;
  }

  .article-tags-grid {
    grid-template-columns: 1fr;
  }

  .share-buttons-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .sidebar-sticky {
    position: static;
    top: auto;
  }
}

@media (max-width: 480px) {
  .blog-hero-title {
    font-size: 1.5rem;
  }

  .featured-article-badge {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
  }

  .blog-image {
    height: 200px;
  }

  .share-buttons-grid {
    grid-template-columns: 1fr;
  }

  .blog-content {
    font-size: 0.9375rem;
  }

  .blog-content h1 { font-size: 1.375rem; }
  .blog-content h2 { font-size: 1.25rem; }
  .blog-content h3 { font-size: 1.125rem; }

  .newsletter-signup {
    flex-direction: column;
    gap: 0.75rem;
  }

  .newsletter-signup input,
  .newsletter-signup button {
    width: 100%;
  }
}

/* Event Page Specific Styles */
.event-hero-bg {
  background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
}

.event-content {
  line-height: 1.8;
}

.event-content p {
  margin-bottom: 1.5rem;
}

.event-content h1,
.event-content h2,
.event-content h3,
.event-content h4,
.event-content h5,
.event-content h6 {
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-weight: 600;
  color: #1f2937;
}

.event-content h1 { font-size: 2rem; }
.event-content h2 { font-size: 1.75rem; }
.event-content h3 { font-size: 1.5rem; }
.event-content h4 { font-size: 1.25rem; }

.event-content ul,
.event-content ol {
  margin-bottom: 1.5rem;
  padding-left: 2rem;
}

.event-content li {
  margin-bottom: 0.5rem;
}

.featured-event-badge {
  animation: glow-green 2s infinite alternate;
}

@keyframes glow-green {
  from {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.5);
  }
  to {
    box-shadow: 0 0 30px rgba(34, 197, 94, 0.8);
  }
}

.event-status-badge {
  transition: all 0.3s ease;
}

.event-status-badge.upcoming {
  background: linear-gradient(135deg, #10b981, #059669);
  animation: pulse-green 2s infinite;
}

.event-status-badge.today {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  animation: pulse-red 1s infinite;
}

.event-status-badge.past {
  background: linear-gradient(135deg, #6b7280, #4b5563);
}

@keyframes pulse-green {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
  }
}

@keyframes pulse-red {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }
}

.countdown-timer {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.countdown-item {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.countdown-item:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.event-info-card {
  transition: all 0.3s ease;
}

.event-info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.event-action-button {
  transition: all 0.3s ease;
}

.event-action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Enhanced Mobile Responsiveness for Event Pages */
@media (max-width: 640px) {
  .event-hero-title {
    font-size: 1.875rem;
    line-height: 1.2;
  }

  .event-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .event-actions {
    flex-direction: column;
    width: 100%;
  }

  .event-actions .btn {
    width: 100%;
    justify-content: center;
  }

  .countdown-timer {
    padding: 1rem;
  }

  .countdown-item {
    padding: 0.75rem;
  }

  .countdown-number {
    font-size: 1.5rem;
  }

  .event-info-grid {
    grid-template-columns: 1fr;
  }

  .event-share-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .sidebar-sticky {
    position: static;
    top: auto;
  }
}

@media (max-width: 480px) {
  .event-hero-title {
    font-size: 1.5rem;
  }

  .featured-event-badge {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
  }

  .event-image {
    height: 200px;
  }

  .event-share-grid {
    grid-template-columns: 1fr;
  }

  .countdown-timer {
    padding: 0.75rem;
  }

  .countdown-item {
    padding: 0.5rem;
  }

  .countdown-number {
    font-size: 1.25rem;
  }

  .event-content {
    font-size: 0.9375rem;
  }

  .newsletter-signup {
    flex-direction: column;
    gap: 0.75rem;
  }

  .newsletter-signup input,
  .newsletter-signup button {
    width: 100%;
  }
}

/* Scroll to Top Button */
.scroll-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  z-index: 1000;
}

.scroll-to-top:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.scroll-to-top:active {
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.scroll-to-top.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.scroll-to-top:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.4);
}

.scroll-to-top i {
  transition: transform 0.3s ease;
}

.scroll-to-top:hover i {
  transform: translateY(-2px);
}

/* Responsive adjustments for scroll button */
@media (max-width: 768px) {
  .scroll-to-top {
    bottom: 20px;
    right: 20px;
    width: 45px;
    height: 45px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .scroll-to-top {
    bottom: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
    font-size: 14px;
  }
}

/* Enhanced Contact Form Styles */
.form-group {
  position: relative;
}

.form-group input:focus,
.form-group textarea:focus {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.15);
}

.form-group label {
  transition: all 0.2s ease-in-out;
}

.form-group input:focus + label,
.form-group textarea:focus + label {
  color: #2563eb;
}

/* Contact page specific animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.contact-form-container {
  animation: slideInUp 0.6s ease-out;
}

.contact-info-container {
  animation: slideInUp 0.6s ease-out 0.2s both;
}

/* Enhanced button hover effects */
.btn-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: all 0.3s ease;
}

.btn-gradient:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  transform: translateY(-2px);
  box-shadow: 0 20px 40px -10px rgba(102, 126, 234, 0.4);
}

/* Success page animations */
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.success-container {
  animation: fadeInScale 0.5s ease-out;
}

/* Mobile-first responsive improvements for contact forms */
@media (max-width: 768px) {
  .contact-form-container,
  .contact-info-container {
    padding: 1.5rem;
  }

  .form-group input,
  .form-group textarea {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .btn-gradient {
    padding: 1rem 2rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .contact-form-container {
    padding: 1rem;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .grid.grid-cols-1.sm\\:grid-cols-2 {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Enhanced focus states for accessibility */
.form-group input:focus,
.form-group textarea:focus,
button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Loading spinner for form submission */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Flash message animations */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.flash-message {
  animation: slideInRight 0.3s ease-out;
}

/* Contact form input enhancements */
.form-group input,
.form-group textarea {
  transition: all 0.2s ease-in-out;
}

.form-group input:hover,
.form-group textarea:hover {
  border-color: #93c5fd;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: #9ca3af;
  transition: color 0.2s ease-in-out;
}

.form-group input:focus::placeholder,
.form-group textarea:focus::placeholder {
  color: #d1d5db;
}

/* Success page confetti animation improvements */
@media (prefers-reduced-motion: reduce) {
  .confetti,
  .animate-bounce,
  .animate-pulse,
  .animate-ping {
    animation: none;
  }

  .contact-form-container,
  .contact-info-container,
  .success-container {
    animation: none;
    opacity: 1;
    transform: none;
  }
}

/* Simple Hamburger to X Animation - Override */
#mobile-menu-button .hamburger-line {
  transition: all 0.3s ease-in-out;
  transform-origin: center;
}

/* When menu is open (active state) */
#mobile-menu-button.active .hamburger-line:nth-child(1) {
  transform: translateY(8px) rotate(45deg);
}

#mobile-menu-button.active .hamburger-line:nth-child(2) {
  opacity: 0;
  transform: scaleX(0);
}

#mobile-menu-button.active .hamburger-line:nth-child(3) {
  transform: translateY(-8px) rotate(-45deg);
}

/* Force 7% logo size in header and footer */
.navbar-brand img,
.footer-title img {
  width: 7% !important;
  height: auto !important;
  max-width: 7% !important;
}