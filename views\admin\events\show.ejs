<!-- Admin Event Details -->
<div class="admin-header">
    <div class="flex items-center justify-between">
        <h1 class="admin-title">
            <i class="fas fa-calendar mr-2 text-yellow-600"></i>Event Details
        </h1>
        <div class="flex space-x-3">
            <a href="/admin/events" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-2"></i>Back to Events
            </a>
            <a href="/admin/events/<%= event._id %>/edit" class="btn btn-primary">
                <i class="fas fa-edit mr-2"></i>Edit Event
            </a>
        </div>
    </div>
</div>

<!-- Event Details Card -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <!-- Event Header -->
    <div class="relative">
        <% if (event.image) { %>
            <img src="<%= event.image %>" alt="<%= event.title %>" class="w-full h-64 object-cover">
            <div class="absolute inset-0 bg-black bg-opacity-40"></div>
        <% } else { %>
            <div class="w-full h-64 bg-gradient-to-r from-yellow-400 to-yellow-600"></div>
        <% } %>

        <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold mb-2"><%= event.title %></h1>
                    <div class="flex items-center space-x-4 text-yellow-100">
                        <div class="flex items-center">
                            <i class="fas fa-calendar mr-2"></i>
                            <%= new Date(event.date).toLocaleDateString('en-US', {
                                weekday: 'long',
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric'
                            }) %>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-clock mr-2"></i>
                            <%= new Date(event.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) %>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            <%= event.location %>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col items-end space-y-2">
                    <% if (event.featured) { %>
                        <span class="bg-yellow-500 text-yellow-900 px-3 py-1 rounded-full text-sm font-medium">
                            <i class="fas fa-star mr-1"></i>Featured
                        </span>
                    <% } %>
                    <% if (new Date(event.date) > new Date()) { %>
                        <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                            <i class="fas fa-clock mr-1"></i>Upcoming
                        </span>
                    <% } else { %>
                        <span class="bg-gray-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                            <i class="fas fa-check mr-1"></i>Past Event
                        </span>
                    <% } %>
                </div>
            </div>
        </div>
    </div>

    <!-- Event Content -->
    <div class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Event Description</h2>
                <div class="prose max-w-none text-gray-700 mb-6">
                    <%= event.description %>
                </div>

                <!-- Event Details -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Event Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">Start Date & Time</label>
                            <p class="text-gray-900">
                                <%= new Date(event.date).toLocaleDateString() %> at
                                <%= new Date(event.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) %>
                            </p>
                        </div>
                        <% if (event.endDate) { %>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">End Date & Time</label>
                            <p class="text-gray-900">
                                <%= new Date(event.endDate).toLocaleDateString() %> at
                                <%= new Date(event.endDate).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) %>
                            </p>
                        </div>
                        <% } %>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">Location</label>
                            <p class="text-gray-900"><%= event.location %></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">Organizer</label>
                            <p class="text-gray-900"><%= event.organizer || 'Science Club' %></p>
                        </div>
                        <% if (event.registrationLink) { %>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-600 mb-1">Registration Link</label>
                            <a href="<%= event.registrationLink %>" target="_blank" class="text-blue-600 hover:text-blue-800 underline">
                                <%= event.registrationLink %>
                            </a>
                        </div>
                        <% } %>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Quick Actions -->
                <div class="bg-white border border-gray-200 rounded-lg p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <a href="/admin/events/<%= event._id %>/edit" class="btn btn-primary w-full">
                            <i class="fas fa-edit mr-2"></i>Edit Event
                        </a>
                        <button id="toggle-featured-btn"
                                class="btn <%= event.featured ? 'btn-secondary' : 'btn-outline' %> w-full"
                                data-event-id="<%= event._id %>" data-featured="<%= event.featured %>">
                            <i class="fas fa-star mr-2"></i>
                            <%= event.featured ? 'Remove from Featured' : 'Add to Featured' %>
                        </button>
                        <a href="/events/<%= event._id %>" target="_blank" class="btn btn-outline w-full">
                            <i class="fas fa-external-link-alt mr-2"></i>View Public Page
                        </a>
                        <button onclick="deleteEvent('<%= event._id %>')" class="btn btn-danger w-full">
                            <i class="fas fa-trash mr-2"></i>Delete Event
                        </button>
                    </div>
                </div>

                <!-- Event Stats -->
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Event Statistics</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Created:</span>
                            <span class="text-gray-900">
                                <%= new Date(event.createdAt).toLocaleDateString() %>
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Last Updated:</span>
                            <span class="text-gray-900">
                                <%= new Date(event.updatedAt).toLocaleDateString() %>
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Status:</span>
                            <span class="<%= new Date(event.date) > new Date() ? 'text-green-600' : 'text-gray-600' %>">
                                <%= new Date(event.date) > new Date() ? 'Upcoming' : 'Past' %>
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Featured:</span>
                            <span class="<%= event.featured ? 'text-yellow-600' : 'text-gray-600' %>">
                                <%= event.featured ? 'Yes' : 'No' %>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Setup toggle featured button
    const toggleBtn = document.getElementById('toggle-featured-btn');
    if (toggleBtn) {
        toggleBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const eventId = this.getAttribute('data-event-id');
            const isFeatured = this.getAttribute('data-featured');
            if (eventId) {
                toggleFeatured(eventId, isFeatured);
            }
        });
    }
});

function toggleFeatured(eventId, isFeatured) {
    // Convert string to boolean if needed
    const isCurrentlyFeatured = isFeatured === 'true' || isFeatured === true;

    fetch(`/admin/events/${eventId}/toggle-featured`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ featured: !isCurrentlyFeatured })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`Event ${data.featured ? 'featured' : 'unfeatured'} successfully!`);
            location.reload();
        } else {
            alert(data.error || 'Error updating event status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating event status');
    });
}

function deleteEvent(eventId) {
    if (confirm('Are you sure you want to delete this event? This action cannot be undone.')) {
        fetch(`/admin/events/${eventId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.message) {
                alert('Event deleted successfully');
                window.location.href = '/admin/events';
            } else {
                alert('Error deleting event');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting event');
        });
    }
}
</script>
