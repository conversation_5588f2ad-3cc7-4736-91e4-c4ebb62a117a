<!-- Admin Events Index -->

<div class="admin-header">
    <div class="flex items-center justify-between">
        <h1 class="admin-title">
            <i class="fas fa-calendar mr-2 text-yellow-600"></i>Event Management
        </h1>
        <a href="/admin/events/create" class="btn btn-primary">
            <i class="fas fa-plus mr-2"></i>Add New Event
        </a>
    </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-calendar text-yellow-600"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900"><%= events.length %></h3>
                <p class="text-gray-600">Total Events</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-clock text-green-600"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900">
                    <%= events.filter(e => new Date(e.date) > new Date()).length %>
                </h3>
                <p class="text-gray-600">Upcoming</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-check text-blue-600"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900">
                    <%= events.filter(e => e.isPast).length %>
                </h3>
                <p class="text-gray-600">Completed</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-star text-purple-600"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900">
                    <%= events.filter(e => e.featured).length %>
                </h3>
                <p class="text-gray-600">Featured</p>
            </div>
        </div>
    </div>
</div>



<!-- Events Table -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <% if (events && events.length > 0) { %>
        <table class="table admin-table w-full">
            <thead>
                <tr>
                    <th class="w-16">S.No</th>
                    <th>Event</th>
                    <th>Date & Time</th>
                    <th>Location</th>
                    <th>Status</th>
                    <th class="w-32">Actions</th>
                </tr>
            </thead>
            <tbody id="events-tbody">
                <% events.forEach((event, index) => { %>
                    <tr class="event-row" data-event-id="<%= event._id %>">
                        <td class="text-center font-medium text-gray-900 px-4 py-3 border-r border-gray-200">
                            <span class="inline-flex items-center justify-center w-8 h-8 rounded-full text-sm serial-number">
                                <%= index + 1 %>
                            </span>
                        </td>
                        <td data-title="<%= event.title %>">
                            <div class="flex items-center">
                                <% if (event.image) { %>
                                    <img src="<%= event.image %>"
                                         alt="<%= event.title %>"
                                         class="w-12 h-12 object-cover rounded-lg mr-3 border border-gray-200">
                                <% } else { %>
                                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-calendar text-yellow-600"></i>
                                    </div>
                                <% } %>
                                <div>
                                    <h3 class="font-medium text-gray-900"><%= event.title %></h3>
                                    <p class="text-sm text-gray-500">
                                        <%= event.description.length > 60 ? event.description.substring(0, 60) + '...' : event.description %>
                                    </p>
                                    <% if (event.featured) { %>
                                        <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full mt-1 inline-block">
                                            <i class="fas fa-star mr-1"></i>Featured
                                        </span>
                                    <% } %>
                                </div>
                            </div>
                        </td>
                        <td data-date="<%= event.date %>">
                            <div class="text-sm">
                                <div class="text-gray-900 font-medium">
                                    <%= new Date(event.date).toLocaleDateString() %>
                                </div>
                                <div class="text-gray-500">
                                    <%= new Date(event.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) %>
                                </div>
                            </div>
                        </td>
                        <td data-location="<%= event.location %>">
                            <div class="text-sm">
                                <div class="text-gray-900"><%= event.location %></div>
                                <% if (event.venue) { %>
                                    <div class="text-gray-500"><%= event.venue %></div>
                                <% } %>
                            </div>
                        </td>
                        <td data-status="<%= new Date(event.date) > new Date() ? 'upcoming' : 'past' %>">
                            <div class="flex flex-col gap-1">
                                <% if (new Date(event.date) > new Date()) { %>
                                    <span class="status-badge active">
                                        <i class="fas fa-clock mr-1"></i>Upcoming
                                    </span>
                                <% } else { %>
                                    <span class="status-badge inactive">
                                        <i class="fas fa-check mr-1"></i>Past
                                    </span>
                                <% } %>
                                <% if (event.type) { %>
                                    <span class="text-xs text-gray-500"><%= event.type %></span>
                                <% } %>
                            </div>
                        </td>
                        <td>
                            <div class="flex items-center space-x-2">
                                <a href="/admin/events/<%= event._id %>/edit" class="action-btn edit" title="Edit Event">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button onclick="toggleFeatured('<%= event._id %>', <%= event.featured ? 'true' : 'false' %>)"
                                        class="action-btn <%= event.featured ? 'view' : 'edit' %>"
                                        title="<%= event.featured ? 'Remove from Featured' : 'Add to Featured' %>"
                                        style="<%= event.featured ? 'background-color: #fef3c7; color: #92400e;' : '' %>"
                                        data-event-id="<%= event._id %>" data-featured="<%= event.featured %>">
                                    <i class="fas fa-star"></i>
                                </button>
                                <button onclick="deleteEvent('<%= event._id %>')" class="action-btn delete" title="Delete Event" data-event-id="<%= event._id %>">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                <% }) %>
            </tbody>
        </table>


    <% } else { %>
        <div class="p-12 text-center">
            <div class="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-calendar text-gray-400 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Events Found</h3>
            <p class="text-gray-600 mb-6">Get started by creating your first event.</p>
            <a href="/admin/events/create" class="btn btn-primary">
                <i class="fas fa-plus mr-2"></i>Create First Event
            </a>
        </div>
    <% } %>
</div>

<style>
/* Additional styles for better visibility */
.action-btn.featured {
    background-color: #fef3c7 !important;
    color: #92400e !important;
}

.action-btn.featured:hover {
    background-color: #fde68a !important;
}

.serial-number {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>

<script>
// Test if functions work
function testFunction() {
    alert('JavaScript is working!');
    console.log('Test function called successfully');
}

// Global functions for onclick handlers
function toggleFeatured(eventId, isFeatured) {
    console.log('🌟 Toggle featured called:', eventId, isFeatured);

    if (!eventId) {
        console.error('❌ No event ID provided');
        alert('Error: No event ID provided');
        return false;
    }

    // Convert string to boolean if needed
    const isCurrentlyFeatured = isFeatured === 'true' || isFeatured === true;
    console.log('Currently featured:', isCurrentlyFeatured);

    fetch(`/admin/events/${eventId}/toggle-featured`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ featured: !isCurrentlyFeatured })
    })
    .then(response => {
        console.log('✅ Response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('✅ Response data:', data);
        if (data.success) {
            alert(`Event ${data.featured ? 'featured' : 'unfeatured'} successfully!`);
            location.reload();
        } else {
            alert(data.error || 'Error updating event status');
        }
    })
    .catch(error => {
        console.error('❌ Error:', error);
        alert('Error updating event status: ' + error.message);
    });

    return false; // Prevent default action
}

function deleteEvent(eventId) {
    console.log('🗑️ Delete function called with ID:', eventId);

    if (!eventId) {
        console.error('❌ No event ID provided');
        alert('Error: No event ID provided');
        return false;
    }

    if (confirm('Are you sure you want to delete this event? This action cannot be undone.')) {
        console.log('🗑️ Deleting event:', eventId);

        fetch(`/admin/events/${eventId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            console.log('✅ Delete response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('✅ Delete response data:', data);
            if (data.success) {
                alert('Event deleted successfully!');
                location.reload();
            } else {
                alert(data.error || 'Error deleting event');
            }
        })
        .catch(error => {
            console.error('❌ Delete error:', error);
            alert('Error deleting event: ' + error.message);
        });
    }

    return false; // Prevent default action
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('📅 Events admin page loaded');
    console.log('Events found:', document.querySelectorAll('.event-row').length);
    console.log('Delete buttons found:', document.querySelectorAll('.action-btn.delete').length);
    console.log('Feature buttons found:', document.querySelectorAll('button[onclick*="toggleFeatured"]').length);

    // Add a test button for debugging
    const testBtn = document.createElement('button');
    testBtn.textContent = 'Test JS';
    testBtn.onclick = testFunction;
    testBtn.style.cssText = 'position: fixed; top: 10px; right: 10px; z-index: 9999; background: red; color: white; padding: 5px;';
    document.body.appendChild(testBtn);

    // Add fallback event listeners
    document.querySelectorAll('.action-btn.delete').forEach((btn, index) => {
        console.log(`Setting up delete button ${index + 1}`);
        btn.addEventListener('click', function(e) {
            console.log('🖱️ Delete button clicked via event listener');
            const eventId = this.getAttribute('data-event-id');
            if (eventId) {
                e.preventDefault();
                e.stopPropagation();
                deleteEvent(eventId);
            } else {
                console.error('No event ID found in data attribute');
            }
        });
    });

    document.querySelectorAll('button[data-featured]').forEach((btn, index) => {
        console.log(`Setting up feature button ${index + 1}`);
        btn.addEventListener('click', function(e) {
            console.log('🖱️ Feature button clicked via event listener');
            const eventId = this.getAttribute('data-event-id');
            const isFeatured = this.getAttribute('data-featured');
            if (eventId) {
                e.preventDefault();
                e.stopPropagation();
                toggleFeatured(eventId, isFeatured);
            } else {
                console.error('No event ID found in data attribute');
            }
        });
    });

    console.log('✅ Event listeners setup complete');
});
</script>
