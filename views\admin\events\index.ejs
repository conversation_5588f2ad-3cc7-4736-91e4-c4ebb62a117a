<!-- Admin Events Index -->

<div class="admin-header">
    <div class="flex items-center justify-between">
        <h1 class="admin-title">
            <i class="fas fa-calendar mr-2 text-yellow-600"></i>Event Management
        </h1>
        <a href="/admin/events/create" class="btn btn-primary">
            <i class="fas fa-plus mr-2"></i>Add New Event
        </a>
    </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-calendar text-yellow-600"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900"><%= events.length %></h3>
                <p class="text-gray-600">Total Events</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-clock text-green-600"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900">
                    <%= events.filter(e => new Date(e.date) > new Date()).length %>
                </h3>
                <p class="text-gray-600">Upcoming</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-check text-blue-600"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900">
                    <%= events.filter(e => e.isPast).length %>
                </h3>
                <p class="text-gray-600">Completed</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-star text-purple-600"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900">
                    <%= events.filter(e => new Date(e.date) < new Date()).length %>
                </h3>
                <p class="text-gray-600">Past Events</p>
            </div>
        </div>
    </div>
</div>



<!-- Events Table -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <% if (events && events.length > 0) { %>
        <table class="table admin-table w-full">
            <thead>
                <tr>
                    <th class="w-16">S.No</th>
                    <th>Event</th>
                    <th>Date & Time</th>
                    <th>Location</th>
                    <th>Status</th>
                    <th class="w-32">Actions</th>
                </tr>
            </thead>
            <tbody id="events-tbody">
                <% events.forEach((event, index) => { %>
                    <tr class="event-row" data-event-id="<%= event._id %>">
                        <td class="text-center font-medium text-gray-900 px-4 py-3 border-r border-gray-200">
                            <span class="inline-flex items-center justify-center w-8 h-8 rounded-full text-sm serial-number">
                                <%= index + 1 %>
                            </span>
                        </td>
                        <td data-title="<%= event.title %>">
                            <div class="flex items-center">
                                <% if (event.image) { %>
                                    <img src="<%= event.image %>"
                                         alt="<%= event.title %>"
                                         class="w-12 h-12 object-cover rounded-lg mr-3 border border-gray-200">
                                <% } else { %>
                                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-calendar text-yellow-600"></i>
                                    </div>
                                <% } %>
                                <div>
                                    <h3 class="font-medium text-gray-900"><%= event.title %></h3>
                                    <p class="text-sm text-gray-500">
                                        <%= event.description.length > 60 ? event.description.substring(0, 60) + '...' : event.description %>
                                    </p>
                                </div>
                            </div>
                        </td>
                        <td data-date="<%= event.date %>">
                            <div class="text-sm">
                                <div class="text-gray-900 font-medium">
                                    <%= new Date(event.date).toLocaleDateString() %>
                                </div>
                                <div class="text-gray-500">
                                    <%= new Date(event.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) %>
                                </div>
                            </div>
                        </td>
                        <td data-location="<%= event.location %>">
                            <div class="text-sm">
                                <div class="text-gray-900"><%= event.location %></div>
                                <% if (event.venue) { %>
                                    <div class="text-gray-500"><%= event.venue %></div>
                                <% } %>
                            </div>
                        </td>
                        <td data-status="<%= new Date(event.date) > new Date() ? 'upcoming' : 'past' %>">
                            <div class="flex flex-col gap-1">
                                <% if (new Date(event.date) > new Date()) { %>
                                    <span class="status-badge active">
                                        <i class="fas fa-clock mr-1"></i>Upcoming
                                    </span>
                                <% } else { %>
                                    <span class="status-badge inactive">
                                        <i class="fas fa-check mr-1"></i>Past
                                    </span>
                                <% } %>
                                <% if (event.type) { %>
                                    <span class="text-xs text-gray-500"><%= event.type %></span>
                                <% } %>
                            </div>
                        </td>
                        <td>
                            <div class="flex items-center space-x-2">
                                <a href="/admin/events/<%= event._id %>/edit" class="action-btn edit" title="Edit Event">
                                    <i class="fas fa-edit"></i>
                                </a>

                                <!-- JavaScript Delete Button -->
                                <button class="action-btn delete-btn" title="Delete Event (JS)" data-event-id="<%= event._id %>">
                                    <i class="fas fa-trash"></i>
                                </button>

                                <!-- Form-based Delete Button (Fallback) -->
                                <form method="POST" action="/admin/events/<%= event._id %>/delete" style="display: inline;" class="delete-form">
                                    <button type="submit" class="action-btn" title="Delete Event (Form)" style="background-color: #dc2626; color: white;">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                <% }) %>
            </tbody>
        </table>


    <% } else { %>
        <div class="p-12 text-center">
            <div class="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-calendar text-gray-400 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Events Found</h3>
            <p class="text-gray-600 mb-6">Get started by creating your first event.</p>
            <a href="/admin/events/create" class="btn btn-primary">
                <i class="fas fa-plus mr-2"></i>Create First Event
            </a>
        </div>
    <% } %>
</div>

<style>
/* Additional styles for better visibility */
.serial-number {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.delete-btn {
    background-color: #ef4444;
    color: white;
    transition: all 0.3s ease;
}

.delete-btn:hover {
    background-color: #dc2626;
    transform: scale(1.05);
}
</style>

<script>
function deleteEvent(eventId) {
    console.log('🗑️ Delete function called with ID:', eventId);

    if (!eventId) {
        console.error('❌ No event ID provided');
        alert('Error: No event ID provided');
        return;
    }

    if (confirm('Are you sure you want to delete this event? This action cannot be undone.')) {
        console.log('🗑️ Deleting event:', eventId);

        fetch(`/admin/events/${eventId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            console.log('✅ Delete response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('✅ Delete response data:', data);
            if (data.success) {
                alert('Event deleted successfully!');
                location.reload();
            } else {
                alert(data.error || 'Error deleting event');
            }
        })
        .catch(error => {
            console.error('❌ Delete error:', error);
            alert('Error deleting event: ' + error.message);
        });
    }
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('📅 Events admin page loaded');
    console.log('Events found:', document.querySelectorAll('.event-row').length);

    // Add a test button for debugging
    const testBtn = document.createElement('button');
    testBtn.textContent = 'Test Delete Function';
    testBtn.style.cssText = 'position: fixed; top: 10px; right: 10px; z-index: 9999; background: red; color: white; padding: 10px; border: none; border-radius: 5px;';
    testBtn.onclick = function() {
        alert('JavaScript is working!');
        console.log('Test button clicked - JavaScript is working');
    };
    document.body.appendChild(testBtn);

    // Setup delete button event listeners
    const deleteButtons = document.querySelectorAll('.delete-btn');
    console.log('🗑️ Delete buttons found:', deleteButtons.length);

    // Log each button for debugging
    deleteButtons.forEach((btn, index) => {
        console.log(`Button ${index + 1}:`, btn);
        console.log(`Button ${index + 1} event ID:`, btn.getAttribute('data-event-id'));
    });

    deleteButtons.forEach((btn, index) => {
        console.log(`Setting up delete button ${index + 1}`);
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('🖱️ Delete button clicked');
            console.log('Button element:', this);
            console.log('Button classes:', this.className);
            const eventId = this.getAttribute('data-event-id');
            console.log('Event ID from button:', eventId);
            if (eventId) {
                deleteEvent(eventId);
            } else {
                console.error('❌ No event ID found in data attribute');
                alert('Error: No event ID found');
            }
        });
    });

    // Also add click listener to document for debugging
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('delete-btn')) {
            console.log('🔍 Document click detected on delete button');
            console.log('Target:', e.target);
            console.log('Event ID:', e.target.getAttribute('data-event-id'));
        }
    });

    // Setup form-based delete with confirmation
    const deleteForms = document.querySelectorAll('.delete-form');
    console.log('📝 Delete forms found:', deleteForms.length);

    deleteForms.forEach((form, index) => {
        console.log(`Setting up delete form ${index + 1}`);
        form.addEventListener('submit', function(e) {
            console.log('📝 Delete form submitted');
            if (!confirm('Are you sure you want to delete this event? This action cannot be undone.')) {
                e.preventDefault();
                console.log('❌ Delete cancelled by user');
            } else {
                console.log('✅ Delete confirmed by user');
            }
        });
    });

    console.log('✅ Event listeners setup complete');
});
</script>
