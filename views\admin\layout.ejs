<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= typeof title !== 'undefined' ? title + ' - Science Club Admin' : 'Science Club Admin' %></title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                    },
                },
            },
        }
    </script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom Styles -->
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .notification-dot {
            position: absolute;
            top: -1px;
            right: -1px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #ef4444;
        }

        /* Enhanced Form Styles */
        .form-container {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 24px;
            padding: 2rem;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(255, 255, 255, 0.8);
        }

        .form-section {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
        }

        .form-section-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e2e8f0;
            display: flex;
            align-items: center;
        }

        .form-section-title i {
            margin-right: 0.5rem;
            color: #3b82f6;
        }

        .form-input, .form-textarea, .form-select {
            transition: all 0.2s ease-in-out;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 0.875rem 1rem;
            background: #f8fafc;
            font-size: 0.875rem;
        }

        .form-input:focus, .form-textarea:focus, .form-select:focus {
            background: white;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            transform: translateY(-1px);
        }

        .form-input:hover, .form-textarea:hover, .form-select:hover {
            border-color: #cbd5e1;
        }

        .form-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            letter-spacing: 0.025em;
        }

        .form-label.required::after {
            content: ' *';
            color: #ef4444;
            font-weight: bold;
        }

        .form-help {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 0.25rem;
            font-style: italic;
        }

        .btn {
            transition: all 0.2s ease-in-out;
            font-weight: 600;
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-size: 0.875rem;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            border: 2px solid transparent;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
        }

        .btn-secondary {
            background: white;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        }

        .file-upload-area {
            border: 2px dashed #cbd5e1;
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            background: #f8fafc;
            transition: all 0.2s ease-in-out;
            cursor: pointer;
        }

        .file-upload-area:hover {
            border-color: #3b82f6;
            background: #eff6ff;
        }

        .file-upload-area.dragover {
            border-color: #3b82f6;
            background: #dbeafe;
            transform: scale(1.02);
        }

        .current-file-preview {
            background: white;
            border-radius: 12px;
            padding: 1rem;
            border: 2px solid #e2e8f0;
            display: inline-block;
            margin-bottom: 1rem;
        }

        .current-file-preview img {
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .admin-header {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            padding: 2rem;
            border-radius: 20px;
            margin-bottom: 2rem;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .admin-title {
            font-size: 1.875rem;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        @media (min-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr 1fr;
            }
        }

        .form-actions {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            border-top: 3px solid #e2e8f0;
            margin-top: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .checkbox-wrapper {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 1rem;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
        }

        .checkbox-wrapper:hover {
            border-color: #3b82f6;
            background: #f8fafc;
        }

        .checkbox-wrapper input[type="checkbox"] {
            margin-right: 0.75rem;
            width: 1.25rem;
            height: 1.25rem;
        }
    </style>

    <%- typeof head !== 'undefined' ? head : '' %>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <aside class="hidden md:flex md:flex-shrink-0">
            <div class="flex flex-col w-64 border-r border-gray-200 bg-white">
                <!-- Logo -->
                <div class="flex items-center h-16 px-4 border-b border-gray-200 bg-white">
                    <a href="/admin" class="flex items-center space-x-3">
                        <img src="/images/logo.jpg" alt="Science Club Turbat Logo" class="w-8 h-8 object-contain rounded">
                        <span class="text-xl font-bold text-gray-900">Science Club</span>
                    </a>
                </div>

                <!-- Navigation -->
                <nav class="flex-1 px-2 py-4 space-y-6 overflow-y-auto">
                    <!-- Dashboard -->
                    <div class="space-y-1">
                        <a href="/admin/dashboard" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md hover:bg-gray-50 hover:text-primary-600 <%= path === '/admin/dashboard' ? 'text-primary-600 bg-primary-50' : 'text-gray-700' %>">
                            <i class="fas fa-gauge-high w-6 h-6 mr-3"></i>
                            Dashboard
                        </a>
                    </div>

                    <!-- Content Management -->
                    <div>
                        <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">Content Management</h3>
                        <div class="mt-2 space-y-1">
                            <a href="/admin/projects" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md hover:bg-gray-50 hover:text-primary-600 <%= path.startsWith('/admin/projects') ? 'text-primary-600 bg-primary-50' : 'text-gray-700' %>">
                                <i class="fas fa-flask w-6 h-6 mr-3"></i>
                                Projects
                            </a>

                            <a href="/admin/events" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md hover:bg-gray-50 hover:text-primary-600 <%= path.startsWith('/admin/events') ? 'text-primary-600 bg-primary-50' : 'text-gray-700' %>">
                                <i class="fas fa-calendar w-6 h-6 mr-3"></i>
                                Events
                            </a>
                            <a href="/admin/resources" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md hover:bg-gray-50 hover:text-primary-600 <%= path.startsWith('/admin/resources') ? 'text-primary-600 bg-primary-50' : 'text-gray-700' %>">
                                <i class="fas fa-book w-6 h-6 mr-3"></i>
                                Resources
                            </a>
                        </div>
                    </div>

                    <!-- Communication -->
                    <div>
                        <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">Communication</h3>
                        <div class="mt-2 space-y-1">
                            <a href="/admin/contacts" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md hover:bg-gray-50 hover:text-primary-600 <%= path.startsWith('/admin/contacts') ? 'text-primary-600 bg-primary-50' : 'text-gray-700' %>">
                                <i class="fas fa-envelope w-6 h-6 mr-3"></i>
                                Messages
                                <% if (typeof stats !== 'undefined' && stats.unreadContacts > 0) { %>
                                <span class="ml-auto bg-red-100 text-red-600 py-0.5 px-2 rounded-full text-xs"><%= stats.unreadContacts %></span>
                                <% } %>
                            </a>
                        </div>
                    </div>

                    <!-- User Management -->
                    <% if (user && user.role === 'admin') { %>
                    <div>
                        <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">User Management</h3>
                        <div class="mt-2 space-y-1">
                            <a href="/admin/users" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md hover:bg-gray-50 hover:text-primary-600 <%= path.startsWith('/admin/users') ? 'text-primary-600 bg-primary-50' : 'text-gray-700' %>">
                                <i class="fas fa-users w-6 h-6 mr-3"></i>
                                User Management
                            </a>
                        </div>
                    </div>
                    <% } %>
                </nav>

                <!-- User Menu -->
                <div class="flex-shrink-0 border-t border-gray-200 p-4">
                    <div class="group block w-full flex items-center">
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-700 group-hover:text-gray-900"><%= user ? user.name : 'Admin User' %></p>
                            <p class="text-xs font-medium text-gray-500 group-hover:text-gray-700"><%= user ? user.email : '<EMAIL>' %></p>
                        </div>
                        <a href="/admin/logout" class="ml-auto text-sm text-red-600 hover:text-red-800">
                            <i class="fas fa-sign-out-alt"></i>
                        </a>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <div class="flex flex-col w-0 flex-1 overflow-hidden">
            <!-- Top Navigation -->
            <div class="relative z-10 flex-shrink-0 flex h-16 bg-white border-b border-gray-200">
                <!-- Mobile menu button -->
                <button type="button" id="admin-mobile-menu-btn" class="md:hidden px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500">
                    <span class="sr-only">Open sidebar</span>
                    <i class="fas fa-bars"></i>
                </button>

             
            </div>

            <!-- Main Content Area -->
            <main class="flex-1 relative overflow-y-auto focus:outline-none">
                <div class="py-6">
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
                        <!-- Flash Messages -->
                        <% if (typeof messages !== 'undefined') { %>
                            <% if (messages.success) { %>
                                <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                                    <span class="block sm:inline"><%= messages.success %></span>
                                </div>
                            <% } %>
                            <% if (messages.error) { %>
                                <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                                    <span class="block sm:inline"><%= messages.error %></span>
                                </div>
                            <% } %>
                        <% } %>

                        <!-- Page Content -->
                        <%- body %>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Mobile Navigation Script -->
    <script>
        function toggleSidebar() {
            const sidebar = document.querySelector('aside');
            sidebar.classList.toggle('hidden');
        }

        // Setup mobile menu button
        document.addEventListener('DOMContentLoaded', function() {
            const menuButton = document.getElementById('admin-mobile-menu-btn');
            if (menuButton) {
                menuButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    toggleSidebar();
                });
            }
        });

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.querySelector('aside');
            const menuButton = document.getElementById('admin-mobile-menu-btn');
            if (menuButton && !sidebar.contains(event.target) && !menuButton.contains(event.target)) {
                sidebar.classList.add('hidden');
            }
        });
    </script>
</body>
</html>
