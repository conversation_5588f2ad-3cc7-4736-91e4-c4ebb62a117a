const express = require('express');
const router = express.Router();
const adminController = require('../controllers/adminController');
const projectController = require('../controllers/projectController');
const resourceController = require('../controllers/resourceController');
const eventController = require('../controllers/eventController');
const contactController = require('../controllers/contactController');

// Authentication routes
router.get('/login', adminController.getLoginPage);
router.post('/login', adminController.login);
router.get('/logout', adminController.logout);

// Protected routes (require authentication)
router.use(adminController.requireAuth);

// Dashboard
router.get('/', adminController.getDashboard);
router.get('/dashboard', adminController.getDashboard);

// User management
router.get('/users', adminController.getUserManagement);
router.get('/users/create', adminController.getCreateUserForm);
router.post('/users/create', adminController.createUser);
router.delete('/users/:id', adminController.deleteUser);

// API endpoints
router.get('/api/notifications', adminController.getNotificationsAPI);
router.post('/api/notifications/read', adminController.markNotificationRead);

// Projects management
router.get('/projects', projectController.getAdminProjects);
router.get('/projects/new', projectController.getCreateProjectForm);
router.get('/projects/create', projectController.getCreateProjectForm);
router.post('/projects/create', projectController.createProject);
router.get('/projects/:id/edit', projectController.getEditProjectForm);
router.put('/projects/:id', projectController.updateProject);
router.delete('/projects/:id', projectController.deleteProject);
router.post('/projects/:id/toggle-featured', projectController.toggleProjectFeatured);
router.post('/projects/bulk-action', projectController.bulkActionProjects);



// Events management
router.get('/events', eventController.getAdminEvents);
router.get('/events/new', eventController.getCreateEventForm);
router.get('/events/create', eventController.getCreateEventForm);
router.post('/events/create', eventController.createEvent);
router.get('/events/:id/edit', eventController.getEditEventForm);
router.put('/events/:id', eventController.updateEvent);
router.post('/events/:id', eventController.updateEvent); // Handle POST requests too (for debugging)
router.delete('/events/:id', eventController.deleteEvent);
router.post('/events/:id/toggle-featured', eventController.toggleEventFeatured);
router.post('/events/:id/gallery', eventController.uploadGalleryImages);
router.delete('/events/:eventId/gallery/:imageIndex', eventController.deleteGalleryImage);
router.post('/events/bulk-action', eventController.bulkActionEvents);
router.get('/events/:id', eventController.getAdminEventById);

// Resources management
router.get('/resources', resourceController.getAdminResources);
router.get('/resources/new', resourceController.getCreateResourceForm);
router.get('/resources/create', resourceController.getCreateResourceForm);
router.post('/resources/create', resourceController.createResource);
router.get('/resources/:id/edit', resourceController.getEditResourceForm);
router.put('/resources/:id', resourceController.updateResource);
router.delete('/resources/:id', resourceController.deleteResource);
router.post('/resources/:id/toggle-featured', resourceController.toggleResourceFeatured);
router.post('/resources/bulk-action', resourceController.bulkActionResources);

// Contact messages management
router.get('/contacts', contactController.getContacts);
router.get('/contacts/:id', contactController.viewContact);
router.post('/contacts/:id/delete', contactController.deleteContact);
router.post('/contacts/:id/toggle-read', contactController.toggleRead);

module.exports = router;
